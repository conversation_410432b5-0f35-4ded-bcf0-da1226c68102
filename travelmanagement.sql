/*
 Navicat Premium Data Transfer

 Source Server         : Hotel
 Source Server Type    : MySQL
 Source Server Version : 80030
 Source Host           : localhost:3306
 Source Schema         : travelmanagement

 Target Server Type    : MySQL
 Target Server Version : 80030
 File Encoding         : 65001

 Date: 10/06/2025 13:29:50
*/

SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;

-- ----------------------------
-- Table structure for expensesummary
-- ----------------------------
DROP TABLE IF EXISTS `expensesummary`;
CREATE TABLE `expensesummary`  (
  `id` int(0) NOT NULL AUTO_INCREMENT,
  `projectid` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '项目ID',
  `expensetype` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '消费类型',
  `expenseamount` decimal(10, 2) NOT NULL COMMENT '消费金额',
  `price` decimal(10, 2) DEFAULT NULL COMMENT '价格',
  `quantity` int(0) DEFAULT NULL COMMENT '数量',
  `expensedate` date NOT NULL COMMENT '消费日期',
  `expensenotes` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci COMMENT '备注',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `projectid`(`projectid`) USING BTREE,
  CONSTRAINT `expensesummary_ibfk_1` FOREIGN KEY (`projectid`) REFERENCES `travelproject` (`projectid`) ON DELETE CASCADE ON UPDATE RESTRICT
) ENGINE = InnoDB AUTO_INCREMENT = 11 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of expensesummary
-- ----------------------------
INSERT INTO `expensesummary` VALUES (1, 'TP2023001', '机票', 15000.00, 3000.00, 5, '2023-03-24', '东京往返机票');
INSERT INTO `expensesummary` VALUES (2, 'TP2023001', '住宿', 10000.00, 2000.00, 5, '2023-03-25', '东京希尔顿酒店4晚');
INSERT INTO `expensesummary` VALUES (3, 'TP2023002', '门票', 2500.00, 500.00, 5, '2023-05-11', '兵马俑门票');
INSERT INTO `expensesummary` VALUES (4, 'TP2023003', '餐饮', 6000.00, 1200.00, 5, '2023-07-16', '海鲜自助晚餐');
INSERT INTO `expensesummary` VALUES (5, 'TP2023004', '交通', 3000.00, 600.00, 5, '2023-09-21', '丽江市内交通费用');
INSERT INTO `expensesummary` VALUES (6, 'TP2023005', '保险', 2500.00, 500.00, 5, '2023-12-09', '境外旅游保险');
INSERT INTO `expensesummary` VALUES (7, 'TP2023006', '导游费', 5000.00, 1000.00, 5, '2023-10-06', '专业埃及历史讲解员');
INSERT INTO `expensesummary` VALUES (8, 'TP2023007', '活动费', 7500.00, 1500.00, 5, '2023-11-21', '水上运动项目套餐');
INSERT INTO `expensesummary` VALUES (9, 'TP2023008', '租车费', 6000.00, 1200.00, 5, '2024-01-16', '新西兰自驾车租赁');
INSERT INTO `expensesummary` VALUES (10, 'TP2023009', '购物', 10000.00, 2000.00, 5, '2024-04-12', '意大利奢侈品购物');

-- ----------------------------
-- Table structure for projectteam
-- ----------------------------
DROP TABLE IF EXISTS `projectteam`;
CREATE TABLE `projectteam`  (
  `id` int(0) NOT NULL AUTO_INCREMENT,
  `projectid` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '项目ID',
  `memberid` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '成员ID',
  `memberrole` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '角色类别',
  `isteamleader` tinyint(1) DEFAULT 0 COMMENT '团队负责人',
  `isprojectmanager` tinyint(1) DEFAULT 0 COMMENT '项目管理员',
  `isregularmember` tinyint(1) DEFAULT 0 COMMENT '普通成员',
  `createtime` datetime(0) NOT NULL COMMENT '创建时间',
  PRIMARY KEY (`id`) USING BTREE,
  INDEX `projectid`(`projectid`) USING BTREE,
  CONSTRAINT `projectteam_ibfk_1` FOREIGN KEY (`projectid`) REFERENCES `travelproject` (`projectid`) ON DELETE CASCADE ON UPDATE RESTRICT
) ENGINE = InnoDB AUTO_INCREMENT = 11 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of projectteam
-- ----------------------------
INSERT INTO `projectteam` VALUES (1, 'TP2023001', 'guide001', '领队', 1, 1, 0, '2023-01-10 10:00:00');
INSERT INTO `projectteam` VALUES (2, 'TP2023001', 'user001', '导游', 0, 0, 1, '2023-01-10 10:30:00');
INSERT INTO `projectteam` VALUES (3, 'TP2023002', 'guide001', '领队', 1, 1, 0, '2023-02-20 09:00:00');
INSERT INTO `projectteam` VALUES (4, 'TP2023003', 'agent001', '项目经理', 0, 1, 0, '2023-03-25 14:00:00');
INSERT INTO `projectteam` VALUES (5, 'TP2023004', 'vip001', '特邀顾问', 0, 0, 1, '2023-04-15 11:00:00');
INSERT INTO `projectteam` VALUES (6, 'TP2023005', 'guide002', '领队', 1, 1, 0, '2023-05-10 09:30:00');
INSERT INTO `projectteam` VALUES (7, 'TP2023006', 'agent002', '项目经理', 0, 1, 0, '2023-06-20 14:30:00');
INSERT INTO `projectteam` VALUES (8, 'TP2023007', 'user002', '客户代表', 0, 0, 1, '2023-07-05 16:00:00');
INSERT INTO `projectteam` VALUES (9, 'TP2023008', 'guide001', '领队', 1, 0, 0, '2023-08-15 10:45:00');
INSERT INTO `projectteam` VALUES (10, 'TP2023009', 'admin001', '审核员', 0, 1, 0, '2023-09-10 11:30:00');

-- ----------------------------
-- Table structure for travelproject
-- ----------------------------
DROP TABLE IF EXISTS `travelproject`;
CREATE TABLE `travelproject`  (
  `projectid` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '项目ID',
  `projectname` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '项目名称',
  `createtime` datetime(0) NOT NULL COMMENT '发生年份',
  `traveldestination` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '旅游地点',
  `startdate` date NOT NULL COMMENT '项目发生日期',
  `projectdetails` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci COMMENT '项目结果',
  `projectstatus` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '项目状态',
  `travelnotes` text CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci COMMENT '旅游备注',
  PRIMARY KEY (`projectid`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of travelproject
-- ----------------------------
INSERT INTO `travelproject` VALUES ('TP2023001', '东京樱花之旅', '2023-01-01 00:00:00', '东京', '2023-03-25', '春季赏樱团，参观东京塔、浅草寺等景点', '已完成', '樱花季节人流量大');
INSERT INTO `travelproject` VALUES ('TP2023002', '西安历史文化游', '2023-02-15 00:00:00', '西安', '2023-05-10', '参观兵马俑、华清池、大雁塔等历史景点', '已完成', '文化遗产探索之旅');
INSERT INTO `travelproject` VALUES ('TP2023003', '三亚海滩度假', '2023-03-20 00:00:00', '三亚', '2023-07-15', '海滩度假、潜水、游艇出海等活动', '已完成', '夏季避暑胜地');
INSERT INTO `travelproject` VALUES ('TP2023004', '云南民族风情游', '2023-04-10 00:00:00', '丽江', '2023-09-20', '参观古城、泸沽湖、玉龙雪山等景点', '进行中', '体验少数民族文化');
INSERT INTO `travelproject` VALUES ('TP2023005', '北欧极光探索', '2023-05-05 00:00:00', '芬兰', '2023-12-10', '观赏极光、冰雪活动、圣诞老人村参观', '计划中', '冬季特色旅游');
INSERT INTO `travelproject` VALUES ('TP2023006', '埃及金字塔探秘', '2023-06-15 00:00:00', '开罗', '2023-10-05', '参观金字塔、狮身人面像、埃及博物馆', '计划中', '古文明探索之旅');
INSERT INTO `travelproject` VALUES ('TP2023007', '巴厘岛蜜月游', '2023-07-01 00:00:00', '巴厘岛', '2023-11-20', '海滩度假、情侣按摩、浪漫晚餐', '计划中', '适合新婚夫妇');
INSERT INTO `travelproject` VALUES ('TP2023008', '新西兰自然风光游', '2023-08-10 00:00:00', '奥克兰', '2024-01-15', '参观霍比特人村、米尔福德峡湾、皇后镇', '计划中', '自然风光之旅');
INSERT INTO `travelproject` VALUES ('TP2023009', '意大利艺术人文游', '2023-09-05 00:00:00', '罗马', '2024-04-10', '参观罗马斗兽场、梵蒂冈、比萨斜塔', '计划中', '艺术与历史之旅');
INSERT INTO `travelproject` VALUES ('TP2023010', '马尔代夫豪华度假', '2023-10-20 00:00:00', '马累', '2024-02-25', '水上别墅、潜水、SPA', '计划中', '奢华度假体验');

-- ----------------------------
-- Table structure for userinfo
-- ----------------------------
DROP TABLE IF EXISTS `userinfo`;
CREATE TABLE `userinfo`  (
  `userid` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '用户ID',
  `password` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '密码',
  `usertype` varchar(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '用户类型',
  `username` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci NOT NULL COMMENT '用户名称',
  `contactinfo` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '联系方式',
  `email` varchar(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '邮箱',
  `idcard` varchar(50) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '身份证号',
  `address` varchar(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_0900_ai_ci DEFAULT NULL COMMENT '地址',
  PRIMARY KEY (`userid`) USING BTREE
) ENGINE = InnoDB CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci ROW_FORMAT = Dynamic;

-- ----------------------------
-- Records of userinfo
-- ----------------------------
INSERT INTO `userinfo` VALUES ('admin001', 'pass123', '管理员', '王管理', '13800138001', '<EMAIL>', '110101199001011234', '北京市朝阳区');
INSERT INTO `userinfo` VALUES ('admin002', 'pass234', '管理员', '刘管理', '13800138002', '<EMAIL>', '110101199002021235', '北京市海淀区');
INSERT INTO `userinfo` VALUES ('agent001', 'passagt', '旅行社', '陈代理', '13500135001', '<EMAIL>', '330101199401015678', '杭州市西湖区');
INSERT INTO `userinfo` VALUES ('agent002', 'passagt2', '旅行社', '孙代理', '13500135002', '<EMAIL>', '330101199402025679', '宁波市海曙区');
INSERT INTO `userinfo` VALUES ('guide001', 'pass789', '导游', '张导游', '13700137001', '<EMAIL>', '440101199201013456', '广州市天河区');
INSERT INTO `userinfo` VALUES ('guide002', 'pass890', '导游', '周导游', '13700137002', '<EMAIL>', '440101199202023457', '深圳市南山区');
INSERT INTO `userinfo` VALUES ('user001', 'pass456', '普通用户', '李游客', '13900139001', '<EMAIL>', '310101199101012345', '上海市徐汇区');
INSERT INTO `userinfo` VALUES ('user002', 'pass567', '普通用户', '杨游客', '13900139002', '<EMAIL>', '320101199102022346', '南京市玄武区');
INSERT INTO `userinfo` VALUES ('vip001', 'passvip', 'VIP用户', '赵贵宾', '13600136001', '<EMAIL>', '510101199301014567', '成都市锦江区');
INSERT INTO `userinfo` VALUES ('vip002', 'passvip2', 'VIP用户', '钱贵宾', '13600136002', '<EMAIL>', '510101199302024568', '重庆市渝中区');

SET FOREIGN_KEY_CHECKS = 1;
