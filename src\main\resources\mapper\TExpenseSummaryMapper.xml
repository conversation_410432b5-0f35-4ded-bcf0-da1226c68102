<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="edu.wlxy.testspringboot1.mapper.TExpenseSummaryMapper">
  <resultMap id="BaseResultMap" type="edu.wlxy.testspringboot1.model.TExpenseSummary">
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="projectid" jdbcType="VARCHAR" property="projectId" />
    <result column="expensetype" jdbcType="VARCHAR" property="expenseType" />
    <result column="expenseamount" jdbcType="DECIMAL" property="expenseAmount" />
    <result column="price" jdbcType="DECIMAL" property="price" />
    <result column="quantity" jdbcType="INTEGER" property="quantity" />
    <result column="expensedate" jdbcType="VARCHAR" property="expenseDate" />
    <result column="expensenotes" jdbcType="VARCHAR" property="expenseNotes" />
  </resultMap>
  
  <sql id="Base_Column_List">
    id, projectid, expensetype, expenseamount, price, quantity, expensedate, expensenotes
  </sql>
  
  <!-- 根据主键查询 -->
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from expensesummary
    where id = #{id,jdbcType=INTEGER}
  </select>
  
  <!-- 根据主键删除 -->
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    delete from expensesummary
    where id = #{id,jdbcType=INTEGER}
  </delete>
  
  <!-- 动态插入 -->
  <insert id="insertSelective" parameterType="edu.wlxy.testspringboot1.model.TExpenseSummary" useGeneratedKeys="true" keyProperty="id">
    insert into expensesummary
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="projectId != null">
        projectid,
      </if>
      <if test="expenseType != null">
        expensetype,
      </if>
      <if test="expenseAmount != null">
        expenseamount,
      </if>
      <if test="price != null">
        price,
      </if>
      <if test="quantity != null">
        quantity,
      </if>
      <if test="expenseDate != null">
        expensedate,
      </if>
      <if test="expenseNotes != null">
        expensenotes,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="projectId != null">
        #{projectId,jdbcType=VARCHAR},
      </if>
      <if test="expenseType != null">
        #{expenseType,jdbcType=VARCHAR},
      </if>
      <if test="expenseAmount != null">
        #{expenseAmount,jdbcType=DECIMAL},
      </if>
      <if test="price != null">
        #{price,jdbcType=DECIMAL},
      </if>
      <if test="quantity != null">
        #{quantity,jdbcType=INTEGER},
      </if>
      <if test="expenseDate != null">
        #{expenseDate,jdbcType=VARCHAR},
      </if>
      <if test="expenseNotes != null">
        #{expenseNotes,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  
  <!-- 动态更新 -->
  <update id="updateByPrimaryKeySelective" parameterType="edu.wlxy.testspringboot1.model.TExpenseSummary">
    update expensesummary
    <set>
      <if test="projectId != null">
        projectid = #{projectId,jdbcType=VARCHAR},
      </if>
      <if test="expenseType != null">
        expensetype = #{expenseType,jdbcType=VARCHAR},
      </if>
      <if test="expenseAmount != null">
        expenseamount = #{expenseAmount,jdbcType=DECIMAL},
      </if>
      <if test="price != null">
        price = #{price,jdbcType=DECIMAL},
      </if>
      <if test="quantity != null">
        quantity = #{quantity,jdbcType=INTEGER},
      </if>
      <if test="expenseDate != null">
        expensedate = #{expenseDate,jdbcType=VARCHAR},
      </if>
      <if test="expenseNotes != null">
        expensenotes = #{expenseNotes,jdbcType=VARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=INTEGER}
  </update>
  
  <!-- 条件查询 -->
  <select id="selectBySelective" parameterType="edu.wlxy.testspringboot1.model.TExpenseSummary" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from expensesummary
    <where>
      <if test="id != null">
        id = #{id,jdbcType=INTEGER}
      </if>
      <if test="projectId != null">
        and projectid = #{projectId,jdbcType=VARCHAR}
      </if>
      <if test="expenseType != null">
        and expensetype = #{expenseType,jdbcType=VARCHAR}
      </if>
      <if test="expenseDate != null">
        and expensedate = #{expenseDate,jdbcType=VARCHAR}
      </if>
    </where>
  </select>
</mapper> 