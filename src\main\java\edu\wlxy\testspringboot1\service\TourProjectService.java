package edu.wlxy.testspringboot1.service;

import edu.wlxy.testspringboot1.model.TourProject;

import java.util.List;

/**
 * 旅游项目服务接口
 */
public interface TourProjectService {
    
    /**
     * 新增旅游项目
     */
    int addTourProject(TourProject tourProject);
    
    /**
     * 删除旅游项目
     */
    int deleteTourProject(String projectId);
    
    /**
     * 更新旅游项目
     */
    int updateTourProject(TourProject tourProject);
    
    /**
     * 根据ID查询旅游项目
     */
    TourProject getTourProjectById(String projectId);
    
    /**
     * 查询所有旅游项目
     */
    List<TourProject> getAllTourProjects();
} 