/* 主内容区域样式 */
.main-content {
    margin-left: 250px;
    padding: 30px;
    background-color: #f5f6fa;
    min-height: 100vh;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
}

/* 页面标题区域 */
.page-header {
    background: #fff;
    border-radius: 10px;
    padding: 20px;
    margin-bottom: 24px;
    box-shadow: 0 2px 12px rgba(0,0,0,0.08);
}

.header-wrapper {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 16px;
}

.header-wrapper h2 {
    font-size: 24px;
    color: #2c3e50;
    margin: 0;
    display: flex;
    align-items: center;
    gap: 10px;
}

.header-wrapper h2 i {
    color: #3498db;
}

.subtitle {
    color: #7f8c8d;
    margin: 0;
    font-size: 14px;
}

/* 快速统计区域 */
.quick-stats {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 20px;
    margin-top: 20px;
}

.stat-item {
    background: #fff;
    border-radius: 8px;
    padding: 20px;
    display: flex;
    align-items: center;
    gap: 15px;
    transition: transform 0.3s;
}

.stat-item:hover {
    transform: translateY(-2px);
}

.stat-item i {
    font-size: 24px;
    color: #3498db;
    background: rgba(52, 152, 219, 0.1);
    padding: 12px;
    border-radius: 8px;
}

.stat-info {
    display: flex;
    flex-direction: column;
}

.stat-value {
    font-size: 24px;
    font-weight: bold;
    color: #2c3e50;
}

.stat-label {
    color: #7f8c8d;
    font-size: 14px;
}

/* 搜索区域样式 */
.search-area {
    background: #fff;
    border-radius: 10px;
    padding: 24px;
    margin-bottom: 24px;
    box-shadow: 0 2px 12px rgba(0,0,0,0.08);
}

.search-header {
    margin-bottom: 20px;
    padding-bottom: 15px;
    border-bottom: 1px solid #eee;
}

.search-header h3 {
    color: #2c3e50;
    margin: 0;
    font-size: 18px;
    display: flex;
    align-items: center;
    gap: 8px;
}

.search-content {
    display: flex;
    flex-direction: column;
    gap: 20px;
}

.search-row {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 20px;
    align-items: end;
}

.search-item {
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.search-item label {
    color: #7f8c8d;
    font-size: 14px;
    display: flex;
    align-items: center;
    gap: 6px;
}

.search-item label i {
    color: #3498db;
}

.form-control {
    padding: 10px 12px;
    border: 1px solid #ddd;
    border-radius: 6px;
    font-size: 14px;
    transition: all 0.3s;
    width: 100%;
}

.form-control:focus {
    border-color: #3498db;
    box-shadow: 0 0 0 3px rgba(52, 152, 219, 0.2);
    outline: none;
}

.search-actions {
    display: flex;
    gap: 12px;
}

.btn-search, .btn-reset {
    padding: 10px 24px;
    border-radius: 6px;
    font-size: 14px;
    display: flex;
    align-items: center;
    gap: 8px;
    cursor: pointer;
    transition: all 0.3s;
}

.btn-search {
    background: #3498db;
    color: #fff;
    border: none;
}

.btn-search:hover {
    background: #2980b9;
}

.btn-reset {
    background: #fff;
    color: #7f8c8d;
    border: 1px solid #ddd;
}

.btn-reset:hover {
    background: #f8f9fa;
}

/* 添加项目表单样式 */
.add-project-form {
    background: #fff;
    border-radius: 10px;
    padding: 24px;
    margin: 24px 0;
    box-shadow: 0 2px 12px rgba(0,0,0,0.08);
}

.form-header {
    margin-bottom: 24px;
    padding-bottom: 15px;
    border-bottom: 1px solid #eee;
}

.form-header h3 {
    color: #2c3e50;
    margin: 0;
    font-size: 18px;
    display: flex;
    align-items: center;
    gap: 8px;
}

.form-content {
    display: flex;
    flex-direction: column;
    gap: 24px;
}

.form-row {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 24px;
}

.form-item {
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.form-item label {
    color: #7f8c8d;
    font-size: 14px;
    display: flex;
    align-items: center;
    gap: 6px;
}

.form-item label i {
    color: #3498db;
}

/* 项目列表表格样式 */
.project-list {
    background: #fff;
    border-radius: 10px;
    padding: 24px;
    box-shadow: 0 2px 12px rgba(0,0,0,0.08);
}

.list-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    padding-bottom: 15px;
    border-bottom: 1px solid #eee;
}

.list-header h3 {
    color: #2c3e50;
    margin: 0;
    font-size: 18px;
    display: flex;
    align-items: center;
    gap: 8px;
}

.list-actions {
    display: flex;
    gap: 12px;
}

.project-list table {
    width: 100%;
    border-collapse: collapse;
    margin-top: 20px;
}

.project-list th {
    background: #f8f9fa;
    color: #2c3e50;
    font-weight: 500;
    padding: 12px 16px;
    text-align: left;
    border-bottom: 2px solid #eee;
}

.project-list td {
    padding: 12px 16px;
    border-bottom: 1px solid #eee;
    color: #2c3e50;
    vertical-align: middle;
}

.project-list tr:hover {
    background-color: #f8f9fa;
}

/* 状态标签样式 */
.status-badge {
    display: inline-block;
    padding: 4px 12px;
    border-radius: 12px;
    font-size: 12px;
    font-weight: 500;
}

.status-已完成 {
    background: #e1f7e7;
    color: #27ae60;
}

.status-进行中 {
    background: #e1f0ff;
    color: #3498db;
}

.status-计划中 {
    background: #fff3e0;
    color: #f39c12;
}

/* 操作按钮样式 */
.action-buttons {
    display: flex;
    gap: 8px;
}

.btn-action {
    width: 32px;
    height: 32px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 4px;
    color: #7f8c8d;
    background: #f8f9fa;
    border: 1px solid #eee;
    transition: all 0.3s;
}

.btn-action:hover {
    color: #3498db;
    border-color: #3498db;
    background: #e1f0ff;
}

/* 按钮容器 */
.form-actions {
    display: flex;
    gap: 1rem;
    margin-top: 2rem;
    padding: 1rem 0;
}

/* 通用按钮样式 */
.btn {
    padding: 0.8rem 2rem;
    border-radius: 8px;
    font-size: 1rem;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.3s ease;
    border: none;
    display: flex;
    align-items: center;
    gap: 0.5rem;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

/* 提交按钮 */
.btn-submit {
    background: linear-gradient(135deg, #3498db, #2980b9);
    color: white;
    border: 1px solid transparent;
}

.btn-submit:hover {
    background: linear-gradient(135deg, #2980b9, #2573a7);
    transform: translateY(-1px);
    box-shadow: 0 4px 6px rgba(0,0,0,0.15);
}

.btn-submit:active {
    transform: translateY(0);
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

/* 取消按钮 */
.btn-cancel {
    background: white;
    color: #666;
    border: 1px solid #ddd;
}

.btn-cancel:hover {
    background: #f8f9fa;
    color: #333;
    border-color: #ccc;
    transform: translateY(-1px);
    box-shadow: 0 4px 6px rgba(0,0,0,0.1);
}

.btn-cancel:active {
    transform: translateY(0);
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

/* 按钮图标 */
.btn i {
    font-size: 1rem;
}

/* 禁用状态 */
.btn:disabled {
    opacity: 0.7;
    cursor: not-allowed;
}

/* 分页样式 */
.pagination {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-top: 20px;
    padding-top: 20px;
    border-top: 1px solid #eee;
}

.page-info {
    color: #7f8c8d;
    font-size: 14px;
}

.page-buttons {
    display: flex;
    gap: 8px;
}

.btn-page {
    width: 32px;
    height: 32px;
    display: flex;
    align-items: center;
    justify-content: center;
    border: 1px solid #ddd;
    border-radius: 4px;
    color: #7f8c8d;
    font-size: 14px;
    transition: all 0.3s;
    text-decoration: none;
}

.btn-page:hover {
    color: #3498db;
    border-color: #3498db;
}

.btn-page.active {
    background: #3498db;
    color: #fff;
    border-color: #3498db;
}

.page-ellipsis {
    color: #7f8c8d;
    padding: 0 8px;
}

/* 导出和批量删除按钮 */
.btn-export,
.btn-batch {
    padding: 8px 16px;
    border-radius: 4px;
    font-size: 14px;
    display: flex;
    align-items: center;
    gap: 6px;
    cursor: pointer;
    transition: all 0.3s;
}

.btn-export {
    background: #27ae60;
    color: #fff;
    border: none;
}

.btn-export:hover {
    background: #219a52;
}

.btn-batch {
    background: #e74c3c;
    color: #fff;
    border: none;
}

.btn-batch:hover {
    background: #c0392b;
}

/* 响应式布局 */
@media (max-width: 768px) {
    .main-content {
        margin-left: 0;
        padding: 15px;
    }

    .form-row {
        grid-template-columns: 1fr;
    }

    .search-row {
        grid-template-columns: 1fr;
    }

    .search-actions {
        flex-direction: column;
    }

    .btn-search, .btn-reset {
        width: 100%;
    }
}