package edu.wlxy.testspringboot1.service;

import edu.wlxy.testspringboot1.model.TExpenseSummary;

import java.util.List;

/**
 * 消费账单服务接口
 */
public interface TExpenseSummaryService {
    
    /**
     * 新增消费账单
     */
    int addExpenseSummary(TExpenseSummary expenseSummary);
    
    /**
     * 删除消费账单
     */
    int deleteExpenseSummary(Integer id);
    
    /**
     * 更新消费账单
     */
    int updateExpenseSummary(TExpenseSummary expenseSummary);
    
    /**
     * 根据ID查询消费账单
     */
    TExpenseSummary getExpenseSummaryById(Integer id);
    
    /**
     * 条件查询消费账单
     */
    List<TExpenseSummary> getExpenseSummariesByCondition(TExpenseSummary expenseSummary);
} 