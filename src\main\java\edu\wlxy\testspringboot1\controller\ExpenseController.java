package edu.wlxy.testspringboot1.controller;

import edu.wlxy.testspringboot1.model.TExpenseSummary;
import edu.wlxy.testspringboot1.model.TTravelProject;
import edu.wlxy.testspringboot1.service.TExpenseSummaryService;
import edu.wlxy.testspringboot1.service.TTravelProjectService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.mvc.support.RedirectAttributes;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpSession;
import java.text.SimpleDateFormat;
import java.util.*;

/**
 * 消费账单控制器
 */
@Controller
@RequestMapping("/expense")
public class ExpenseController {
    
    @Autowired
    private TExpenseSummaryService expenseSummaryService;
    
    @Autowired
    private TTravelProjectService travelProjectService;
    
    /**
     * 跳转到添加消费账单页面
     */
    @GetMapping("/toAdd")
    public String toAdd(Model model, HttpSession session) {
        // 检查用户是否登录
        if (session.getAttribute("user") == null) {
            return "redirect:/user/toLogin";
        }
        
        // 获取所有项目
        TTravelProject query = new TTravelProject();
        List<TTravelProject> projects = travelProjectService.getTravelProjectsByCondition(query);
        model.addAttribute("projects", projects);
        
        // 获取最近的消费记录
        List<Map<String, Object>> recentExpenses = getRecentExpensesWithDetails(5);
        model.addAttribute("recentExpenses", recentExpenses);
        
        return "add-expense";
    }
    
    /**
     * 添加消费账单
     */
    @PostMapping("/add")
    public String add(TExpenseSummary expenseSummary, RedirectAttributes redirectAttributes) {
        // 设置消费日期（如果为空）
        if (expenseSummary.getExpenseDate() == null || expenseSummary.getExpenseDate().isEmpty()) {
            SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
            expenseSummary.setExpenseDate(sdf.format(new Date()));
        }
        
        int result = expenseSummaryService.addExpenseSummary(expenseSummary);
        
        if (result > 0) {
            redirectAttributes.addFlashAttribute("successMsg", "消费账单添加成功");
        } else {
            redirectAttributes.addFlashAttribute("errorMsg", "消费账单添加失败");
        }
        
        return "redirect:/expense/toAdd";
    }
    
    /**
     * 删除消费账单
     */
    @GetMapping("/delete/{id}")
    public String delete(@PathVariable("id") Integer id, RedirectAttributes redirectAttributes, HttpServletRequest request) {
        int result = expenseSummaryService.deleteExpenseSummary(id);
        
        if (result > 0) {
            redirectAttributes.addFlashAttribute("successMsg", "消费账单删除成功");
        } else {
            redirectAttributes.addFlashAttribute("errorMsg", "消费账单删除失败");
        }
        
        // 获取来源页面，决定重定向到哪个页面
        String referer = request.getHeader("Referer");
        if (referer != null && referer.contains("/expense/view")) {
            return "redirect:/expense/view";
        } else {
            return "redirect:/expense/toAdd";
        }
    }
    
    /**
     * 跳转到编辑消费账单页面
     */
    @GetMapping("/edit/{id}")
    public String toEdit(@PathVariable("id") Integer id, Model model, HttpSession session) {
        // 检查用户是否登录
        if (session.getAttribute("user") == null) {
            return "redirect:/user/toLogin";
        }
        
        // 获取消费账单信息
        TExpenseSummary expenseSummary = expenseSummaryService.getExpenseSummaryById(id);
        if (expenseSummary == null) {
            return "redirect:/expense/toAdd";
        }
        model.addAttribute("expenseSummary", expenseSummary);
        
        // 获取所有项目
        TTravelProject query = new TTravelProject();
        List<TTravelProject> projects = travelProjectService.getTravelProjectsByCondition(query);
        model.addAttribute("projects", projects);
        
        return "edit-expense";
    }
    
    /**
     * 更新消费账单
     */
    @PostMapping("/update")
    public String update(TExpenseSummary expenseSummary, RedirectAttributes redirectAttributes, HttpServletRequest request) {
        int result = expenseSummaryService.updateExpenseSummary(expenseSummary);
        
        if (result > 0) {
            redirectAttributes.addFlashAttribute("successMsg", "消费账单更新成功");
        } else {
            redirectAttributes.addFlashAttribute("errorMsg", "消费账单更新失败");
        }
        
        // 获取来源页面，决定重定向到哪个页面
        String referer = request.getHeader("Referer");
        if (referer != null && referer.contains("/expense/view")) {
            return "redirect:/expense/view";
        } else {
            return "redirect:/expense/toAdd";
        }
    }
    
    /**
     * 查看消费账单
     */
    @GetMapping("/view")
    public String view(Model model, HttpSession session) {
        // 检查用户是否登录
        if (session.getAttribute("user") == null) {
            return "redirect:/user/toLogin";
        }
        
        // 获取所有项目
        TTravelProject query = new TTravelProject();
        List<TTravelProject> projects = travelProjectService.getTravelProjectsByCondition(query);
        model.addAttribute("projects", projects);
        
        // 获取所有消费账单
        List<Map<String, Object>> expenses = getAllExpensesWithDetails();
        model.addAttribute("expenses", expenses);
        
        // 计算统计信息
        int totalRecords = expenses.size();
        double totalAmount = 0;
        Map<String, Double> expenseTypeMap = new HashMap<>();
        
        for (Map<String, Object> expense : expenses) {
            Double amount = (Double) expense.get("expenseAmount");
            if (amount != null) {
                totalAmount += amount;
            }
            
            String expenseType = (String) expense.get("expenseType");
            if (expenseType != null) {
                Double typeAmount = expenseTypeMap.getOrDefault(expenseType, 0.0);
                expenseTypeMap.put(expenseType, typeAmount + (amount != null ? amount : 0));
            }
        }
        
        double averageAmount = totalRecords > 0 ? totalAmount / totalRecords : 0;
        
        model.addAttribute("totalRecords", totalRecords);
        model.addAttribute("totalAmount", totalAmount);
        model.addAttribute("averageAmount", averageAmount);
        
        // 准备图表数据
        List<String> expenseTypeLabels = new ArrayList<>(expenseTypeMap.keySet());
        List<Double> expenseTypeValues = new ArrayList<>();
        for (String label : expenseTypeLabels) {
            expenseTypeValues.add(expenseTypeMap.get(label));
        }
        
        model.addAttribute("expenseTypeLabels", expenseTypeLabels);
        model.addAttribute("expenseTypeValues", expenseTypeValues);
        
        // 分页信息（简化版，实际应该使用分页插件）
        int currentPage = 1;
        int pageSize = 10;
        int total = expenses.size();
        int totalPages = (total + pageSize - 1) / pageSize;
        int startPage = Math.max(1, currentPage - 2);
        int endPage = Math.min(totalPages, currentPage + 2);
        
        model.addAttribute("total", total);
        model.addAttribute("currentPage", currentPage);
        model.addAttribute("totalPages", totalPages);
        model.addAttribute("startPage", startPage);
        model.addAttribute("endPage", endPage);
        
        return "view-bill";
    }
    
    /**
     * 搜索消费账单
     */
    @PostMapping("/search")
    public String search(
            @RequestParam(required = false) String projectId,
            @RequestParam(required = false) String expenseType,
            @RequestParam(required = false) String startDate,
            @RequestParam(required = false) String endDate,
            @RequestParam(defaultValue = "1") int page,
            Model model,
            HttpSession session) {
        
        // 检查用户是否登录
        if (session.getAttribute("user") == null) {
            return "redirect:/user/toLogin";
        }
        
        // 创建查询条件
        TExpenseSummary query = new TExpenseSummary();
        if (projectId != null && !projectId.isEmpty()) {
            query.setProjectId(projectId);
        }
        if (expenseType != null && !expenseType.isEmpty()) {
            query.setExpenseType(expenseType);
        }
        
        // 获取符合条件的消费账单
        List<TExpenseSummary> expenseSummaries = expenseSummaryService.getExpenseSummariesByCondition(query);
        
        // 过滤日期范围（MyBatis XML中不方便处理日期范围，所以在Java中过滤）
        List<TExpenseSummary> filteredExpenses = new ArrayList<>();
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
        
        try {
            Date start = startDate != null && !startDate.isEmpty() ? sdf.parse(startDate) : null;
            Date end = endDate != null && !endDate.isEmpty() ? sdf.parse(endDate) : null;
            
            for (TExpenseSummary expense : expenseSummaries) {
                if (expense.getExpenseDate() != null && !expense.getExpenseDate().isEmpty()) {
                    Date expenseDate = sdf.parse(expense.getExpenseDate());
                    
                    if ((start == null || !expenseDate.before(start)) && 
                        (end == null || !expenseDate.after(end))) {
                        filteredExpenses.add(expense);
                    }
                }
            }
        } catch (Exception e) {
            // 日期解析错误，使用原始列表
            filteredExpenses = expenseSummaries;
        }
        
        // 转换为带项目名的列表
        List<Map<String, Object>> expenses = new ArrayList<>();
        for (TExpenseSummary expense : filteredExpenses) {
            Map<String, Object> expenseMap = new HashMap<>();
            expenseMap.put("id", expense.getId());
            expenseMap.put("projectId", expense.getProjectId());
            expenseMap.put("expenseType", expense.getExpenseType());
            expenseMap.put("expenseAmount", expense.getExpenseAmount());
            expenseMap.put("price", expense.getPrice());
            expenseMap.put("quantity", expense.getQuantity());
            expenseMap.put("expenseDate", expense.getExpenseDate());
            expenseMap.put("expenseNotes", expense.getExpenseNotes());
            
            // 获取项目名称
            TTravelProject project = travelProjectService.getTravelProjectById(expense.getProjectId());
            if (project != null) {
                expenseMap.put("projectName", project.getProjectName());
            } else {
                expenseMap.put("projectName", "未知项目");
            }
            
            expenses.add(expenseMap);
        }
        
        // 获取所有项目（用于下拉框）
        TTravelProject projectQuery = new TTravelProject();
        List<TTravelProject> projects = travelProjectService.getTravelProjectsByCondition(projectQuery);
        model.addAttribute("projects", projects);
        
        // 计算统计信息
        int totalRecords = expenses.size();
        double totalAmount = 0;
        Map<String, Double> expenseTypeMap = new HashMap<>();
        
        for (Map<String, Object> expense : expenses) {
            Double amount = (Double) expense.get("expenseAmount");
            if (amount != null) {
                totalAmount += amount;
            }
            
            String type = (String) expense.get("expenseType");
            if (type != null) {
                Double typeAmount = expenseTypeMap.getOrDefault(type, 0.0);
                expenseTypeMap.put(type, typeAmount + (amount != null ? amount : 0));
            }
        }
        
        double averageAmount = totalRecords > 0 ? totalAmount / totalRecords : 0;
        
        model.addAttribute("expenses", expenses);
        model.addAttribute("totalRecords", totalRecords);
        model.addAttribute("totalAmount", totalAmount);
        model.addAttribute("averageAmount", averageAmount);
        
        // 准备图表数据
        List<String> expenseTypeLabels = new ArrayList<>(expenseTypeMap.keySet());
        List<Double> expenseTypeValues = new ArrayList<>();
        for (String label : expenseTypeLabels) {
            expenseTypeValues.add(expenseTypeMap.get(label));
        }
        
        model.addAttribute("expenseTypeLabels", expenseTypeLabels);
        model.addAttribute("expenseTypeValues", expenseTypeValues);
        
        // 分页信息
        int pageSize = 10;
        int total = expenses.size();
        int totalPages = (total + pageSize - 1) / pageSize;
        int startPage = Math.max(1, page - 2);
        int endPage = Math.min(totalPages, page + 2);
        
        model.addAttribute("total", total);
        model.addAttribute("currentPage", page);
        model.addAttribute("totalPages", totalPages);
        model.addAttribute("startPage", startPage);
        model.addAttribute("endPage", endPage);
        
        return "view-bill";
    }
    
    /**
     * 导出消费账单
     */
    @GetMapping("/export")
    @ResponseBody
    public Map<String, Object> export() {
        Map<String, Object> result = new HashMap<>();
        result.put("success", true);
        result.put("message", "导出功能尚未实现");
        return result;
    }
    
    /**
     * 获取最近的消费记录（带项目名）
     */
    private List<Map<String, Object>> getRecentExpensesWithDetails(int limit) {
        // 获取所有消费账单
        TExpenseSummary query = new TExpenseSummary();
        List<TExpenseSummary> allExpenses = expenseSummaryService.getExpenseSummariesByCondition(query);
        
        // 按ID降序排序（假设ID越大越新）
        allExpenses.sort((a, b) -> b.getId() - a.getId());
        
        // 只取前limit个
        List<TExpenseSummary> recentExpenses = allExpenses.size() > limit 
                ? allExpenses.subList(0, limit) 
                : allExpenses;
        
        // 转换为带项目名的列表
        List<Map<String, Object>> result = new ArrayList<>();
        for (TExpenseSummary expense : recentExpenses) {
            Map<String, Object> expenseMap = new HashMap<>();
            expenseMap.put("id", expense.getId());
            expenseMap.put("projectId", expense.getProjectId());
            expenseMap.put("expenseType", expense.getExpenseType());
            expenseMap.put("expenseAmount", expense.getExpenseAmount());
            expenseMap.put("price", expense.getPrice());
            expenseMap.put("quantity", expense.getQuantity());
            expenseMap.put("expenseDate", expense.getExpenseDate());
            expenseMap.put("expenseNotes", expense.getExpenseNotes());
            
            // 获取项目名称
            TTravelProject project = travelProjectService.getTravelProjectById(expense.getProjectId());
            if (project != null) {
                expenseMap.put("projectName", project.getProjectName());
            } else {
                expenseMap.put("projectName", "未知项目");
            }
            
            result.add(expenseMap);
        }
        
        return result;
    }
    
    /**
     * 获取所有消费账单（带项目名）
     */
    private List<Map<String, Object>> getAllExpensesWithDetails() {
        // 获取所有消费账单
        TExpenseSummary query = new TExpenseSummary();
        List<TExpenseSummary> allExpenses = expenseSummaryService.getExpenseSummariesByCondition(query);
        
        // 转换为带项目名的列表
        List<Map<String, Object>> result = new ArrayList<>();
        for (TExpenseSummary expense : allExpenses) {
            Map<String, Object> expenseMap = new HashMap<>();
            expenseMap.put("id", expense.getId());
            expenseMap.put("projectId", expense.getProjectId());
            expenseMap.put("expenseType", expense.getExpenseType());
            expenseMap.put("expenseAmount", expense.getExpenseAmount());
            expenseMap.put("price", expense.getPrice());
            expenseMap.put("quantity", expense.getQuantity());
            expenseMap.put("expenseDate", expense.getExpenseDate());
            expenseMap.put("expenseNotes", expense.getExpenseNotes());
            
            // 获取项目名称
            TTravelProject project = travelProjectService.getTravelProjectById(expense.getProjectId());
            if (project != null) {
                expenseMap.put("projectName", project.getProjectName());
            } else {
                expenseMap.put("projectName", "未知项目");
            }
            
            result.add(expenseMap);
        }
        
        return result;
    }
} 