<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>登录 - 旅游项目管理系统</title>
  <link rel="stylesheet" href="${pageContext.request.contextPath}/static/css/login.css">
</head>
<body>
<div class="login-container">
  <div class="login-box">
    <h2>管理员登录</h2>
    <form action="${pageContext.request.contextPath}/user/login" method="post">
      <div class="form-group">
        <label for="username">用户名：</label>
        <input type="text" id="username" name="username" required>
      </div>
      <div class="form-group">
        <label for="password">密码：</label>
        <input type="password" id="password" name="password" required>
      </div>
      <div class="form-buttons">
        <button type="submit">登录</button>
        <a href="${pageContext.request.contextPath}/user/toRegister">注册新账号</a>
      </div>
      <c:if test="${not empty errorMsg}">
        <div class="error-message">
          ${errorMsg}
        </div>
      </c:if>
    </form>
  </div>
</div>
</body>
</html> 