/* register.css - 注册页面专用样式 */
body {
    background-color: #f5f5f5; /* 添加背景色 */
    font-family: Arial, sans-serif; /* 设置字体 */
}

.register-container {
    max-width: 400px;
    margin: 50px auto;
    padding: 30px;
    background: white;
    border-radius: 8px;
    box-shadow: 0 0 10px rgba(0,0,0,0.1);
}

.register-container h2 {
    text-align: center;
    color: #333;
    margin-bottom: 30px;
    font-size: 24px; /* 设置标题大小 */
}

.form-group {
    margin-bottom: 20px;
}

.form-group label {
    display: block;
    margin-bottom: 5px;
    color: #555;
    font-weight: 500; /* 加粗标签文字 */
}

.form-group input {
    width: 100%;
    padding: 10px;
    border: 1px solid #ddd;
    border-radius: 4px;
    font-size: 16px;
    transition: border-color 0.3s ease; /* 添加过渡效果 */
}

/* 输入框焦点效果 */
.form-group input:focus {
    outline: none;
    border-color: #28a745;
    box-shadow: 0 0 0 3px rgba(40, 167, 69, 0.2);
}

.form-buttons {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-top: 30px; /* 增加按钮区域上边距 */
}

.form-buttons button {
    background-color: #28a745;
    color: white;
    padding: 10px 20px;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    font-size: 16px;
    transition: background-color 0.3s ease; /* 添加过渡效果 */
}

/* 按钮悬停效果 */
.form-buttons button:hover {
    background-color: #218838;
}

/* 按钮点击效果 */
.form-buttons button:active {
    transform: translateY(1px);
}

.form-buttons a {
    color: #007bff;
    text-decoration: none;
    transition: color 0.3s ease; /* 添加过渡效果 */
}

/* 链接悬停效果 */
.form-buttons a:hover {
    color: #0056b3;
    text-decoration: underline;
}

/* 添加错误信息样式 */
.error-message {
    color: #dc3545;
    background-color: #f8d7da;
    border: 1px solid #f5c6cb;
    padding: 10px;
    border-radius: 4px;
    margin-bottom: 20px;
    text-align: center;
    font-size: 14px;
}

/* 添加必填字段标记 */
.form-group label::after {
    content: '*';
    color: #dc3545;
    margin-left: 4px;
}

/* 添加响应式设计 */
@media (max-width: 480px) {
    .register-container {
        margin: 20px;
        padding: 20px;
    }

    .form-buttons {
        flex-direction: column;
        gap: 10px;
    }

    .form-buttons button,
    .form-buttons a {
        width: 100%;
        text-align: center;
    }
}