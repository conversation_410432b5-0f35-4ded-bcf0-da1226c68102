package edu.wlxy.testspringboot1.service.impl;

import edu.wlxy.testspringboot1.mapper.TProjectTeamMapper;
import edu.wlxy.testspringboot1.model.TProjectTeam;
import edu.wlxy.testspringboot1.service.TProjectTeamService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 项目团队成员服务实现类
 */
@Service
public class TProjectTeamServiceImpl implements TProjectTeamService {
    
    @Autowired
    private TProjectTeamMapper projectTeamMapper;
    
    @Override
    public int addProjectTeam(TProjectTeam projectTeam) {
        return projectTeamMapper.insertSelective(projectTeam);
    }
    
    @Override
    public int deleteProjectTeam(Integer id) {
        return projectTeamMapper.deleteByPrimaryKey(id);
    }
    
    @Override
    public int updateProjectTeam(TProjectTeam projectTeam) {
        return projectTeamMapper.updateByPrimaryKeySelective(projectTeam);
    }
    
    @Override
    public TProjectTeam getProjectTeamById(Integer id) {
        return projectTeamMapper.selectByPrimaryKey(id);
    }
    
    @Override
    public List<TProjectTeam> getProjectTeamsByCondition(TProjectTeam projectTeam) {
        return projectTeamMapper.selectBySelective(projectTeam);
    }
} 