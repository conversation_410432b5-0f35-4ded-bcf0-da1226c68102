package edu.wlxy.testspringboot1.service;

import edu.wlxy.testspringboot1.model.TUser;

import java.util.List;

/**
 * 用户服务接口
 */
public interface TUserService {
    
    /**
     * 新增用户
     */
    int addUser(TUser user);
    
    /**
     * 删除用户
     */
    int deleteUser(String userId);
    
    /**
     * 更新用户
     */
    int updateUser(TUser user);
    
    /**
     * 根据ID查询用户
     */
    TUser getUserById(String userId);
    
    /**
     * 条件查询用户
     */
    List<TUser> getUsersByCondition(TUser user);
} 