<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<%@ taglib prefix="fmt" uri="http://java.sun.com/jsp/jstl/fmt" %>
<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>查看账单 - 旅游项目管理系统</title>
  <link rel="stylesheet" href="${pageContext.request.contextPath}/static/css/style.css">
  <link rel="stylesheet" href="${pageContext.request.contextPath}/static/fonts/fontawesome-free-6.4.0-web/css/all.min.css">
  <link rel="stylesheet" href="${pageContext.request.contextPath}/static/css/view-bill.css">
  <!-- 引入Chart.js库 -->
  <script src="${pageContext.request.contextPath}/static/js/chart.min.js"></script>
</head>
<body>
<jsp:include page="common/nav.jsp" />

<div class="main-content">
  <div class="container">
    <div class="page-header">
      <div class="header-wrapper">
        <h2><i class="fa fa-file-invoice"></i> 查看账单</h2>
        <p class="subtitle">项目消费账单明细</p>
      </div>
    </div>

    <!-- 搜索条件区域 -->
    <div class="search-area">
      <div class="search-header">
        <h3><i class="fa fa-search"></i> 搜索条件</h3>
      </div>
      <div class="search-content">
        <form id="searchForm" action="${pageContext.request.contextPath}/expense/search" method="post">
          <div class="search-row">
            <div class="search-item">
              <label><i class="fa fa-project-diagram"></i> 项目</label>
              <select name="projectId" class="form-control">
                <option value="">全部项目</option>
                <c:forEach items="${projects}" var="project">
                  <option value="${project.projectId}" ${param.projectId == project.projectId ? 'selected' : ''}>${project.projectName}</option>
                </c:forEach>
              </select>
            </div>
            <div class="search-item">
              <label><i class="fa fa-tag"></i> 消费类型</label>
              <select name="expenseType" class="form-control">
                <option value="">全部类型</option>
                <option value="住宿" ${param.expenseType == '住宿' ? 'selected' : ''}>住宿</option>
                <option value="餐饮" ${param.expenseType == '餐饮' ? 'selected' : ''}>餐饮</option>
                <option value="交通" ${param.expenseType == '交通' ? 'selected' : ''}>交通</option>
                <option value="门票" ${param.expenseType == '门票' ? 'selected' : ''}>门票</option>
                <option value="购物" ${param.expenseType == '购物' ? 'selected' : ''}>购物</option>
                <option value="其他" ${param.expenseType == '其他' ? 'selected' : ''}>其他</option>
              </select>
            </div>
          </div>
          <div class="search-row">
            <div class="search-item">
              <label><i class="fa fa-calendar"></i> 开始日期</label>
              <input type="date" name="startDate" class="form-control" value="${param.startDate}">
            </div>
            <div class="search-item">
              <label><i class="fa fa-calendar"></i> 结束日期</label>
              <input type="date" name="endDate" class="form-control" value="${param.endDate}">
            </div>
            <div class="search-actions">
              <button type="submit" class="btn-search">
                <i class="fa fa-search"></i> 查询
              </button>
              <button type="reset" class="btn-reset">
                <i class="fa fa-refresh"></i> 重置
              </button>
            </div>
          </div>
        </form>
      </div>
    </div>

    <!-- 账单统计 -->
    <div class="bill-summary">
      <div class="summary-header">
        <h3><i class="fa fa-chart-pie"></i> 账单统计</h3>
      </div>
      <div class="summary-cards">
        <div class="summary-card">
          <div class="card-icon">
            <i class="fa fa-receipt"></i>
          </div>
          <div class="card-content">
            <span class="card-value">${totalRecords}</span>
            <span class="card-label">总记录数</span>
          </div>
        </div>
        <div class="summary-card">
          <div class="card-icon">
            <i class="fa fa-money-bill-wave"></i>
          </div>
          <div class="card-content">
            <span class="card-value">¥<fmt:formatNumber value="${totalAmount}" pattern="#,##0.00"/></span>
            <span class="card-label">总金额</span>
          </div>
        </div>
        <div class="summary-card">
          <div class="card-icon">
            <i class="fa fa-calculator"></i>
          </div>
          <div class="card-content">
            <span class="card-value">¥<fmt:formatNumber value="${averageAmount}" pattern="#,##0.00"/></span>
            <span class="card-label">平均金额</span>
          </div>
        </div>
      </div>
      
      <!-- 类型占比图表 -->
      <div class="chart-container">
        <canvas id="expenseTypeChart"></canvas>
      </div>
    </div>

    <!-- 账单列表 -->
    <div class="bill-list">
      <div class="list-header">
        <h3><i class="fa fa-list"></i> 账单明细</h3>
        <div class="list-actions">
          <button class="btn-export" onclick="exportExpenses()">
            <i class="fa fa-download"></i> 导出
          </button>
          <button class="btn-print" onclick="printBill()">
            <i class="fa fa-print"></i> 打印
          </button>
        </div>
      </div>
      <div class="table-responsive">
        <table>
          <thead>
          <tr>
            <th>ID</th>
            <th>项目</th>
            <th>消费类型</th>
            <th>单价</th>
            <th>数量</th>
            <th>总金额</th>
            <th>消费日期</th>
            <th>备注</th>
            <th>操作</th>
          </tr>
          </thead>
          <tbody>
          <c:forEach items="${expenses}" var="expense">
            <tr>
              <td>${expense.id}</td>
              <td>${expense.projectName}</td>
              <td><span class="expense-type expense-type-${expense.expenseType}">${expense.expenseType}</span></td>
              <td>¥<fmt:formatNumber value="${expense.price}" pattern="#,##0.00"/></td>
              <td>${expense.quantity}</td>
              <td>¥<fmt:formatNumber value="${expense.expenseAmount}" pattern="#,##0.00"/></td>
              <td>${expense.expenseDate}</td>
              <td class="expense-notes">${expense.expenseNotes}</td>
              <td class="action-buttons">
                <a href="${pageContext.request.contextPath}/expense/edit/${expense.id}" class="btn-action" title="编辑">
                  <i class="fa fa-pencil"></i>
                </a>
                <a href="javascript:void(0)" onclick="deleteExpense(${expense.id})" class="btn-action" title="删除">
                  <i class="fa fa-trash"></i>
                </a>
              </td>
            </tr>
          </c:forEach>
          </tbody>
        </table>
      </div>
      
      <!-- 分页 -->
      <div class="pagination">
        <span class="page-info">共 ${total} 条记录</span>
        <div class="page-buttons">
          <c:if test="${currentPage > 1}">
            <a href="javascript:void(0)" onclick="goToPage(1)" class="btn-page" title="首页">
              <i class="fa fa-angle-double-left"></i>
            </a>
            <a href="javascript:void(0)" onclick="goToPage(${currentPage - 1})" class="btn-page" title="上一页">
              <i class="fa fa-angle-left"></i>
            </a>
          </c:if>
          
          <c:forEach begin="${startPage}" end="${endPage}" var="i">
            <a href="javascript:void(0)" onclick="goToPage(${i})" class="btn-page ${i == currentPage ? 'active' : ''}">${i}</a>
          </c:forEach>
          
          <c:if test="${currentPage < totalPages}">
            <a href="javascript:void(0)" onclick="goToPage(${currentPage + 1})" class="btn-page" title="下一页">
              <i class="fa fa-angle-right"></i>
            </a>
            <a href="javascript:void(0)" onclick="goToPage(${totalPages})" class="btn-page" title="末页">
              <i class="fa fa-angle-double-right"></i>
            </a>
          </c:if>
        </div>
      </div>
    </div>
  </div>
</div>

<script>
  function goToPage(page) {
    const form = document.getElementById('searchForm');
    const pageInput = document.createElement('input');
    pageInput.type = 'hidden';
    pageInput.name = 'page';
    pageInput.value = page;
    form.appendChild(pageInput);
    form.submit();
  }
  
  function deleteExpense(id) {
    if (confirm('确定要删除此消费记录吗？')) {
      location.href = '${pageContext.request.contextPath}/expense/delete/' + id;
    }
  }
  
  function exportExpenses() {
    window.location.href = '${pageContext.request.contextPath}/expense/export';
  }
  
  function printBill() {
    window.print();
  }
  
  // 绘制图表
  document.addEventListener('DOMContentLoaded', function() {
    try {
      const ctx = document.getElementById('expenseTypeChart').getContext('2d');
      
      // 检查是否有数据
      const labels = ${expenseTypeLabels || '["无数据"]'};
      const values = ${expenseTypeValues || '[100]'};
      
      const expenseTypeChart = new Chart(ctx, {
        type: 'pie',
        data: {
          labels: labels,
          datasets: [{
            label: '消费金额',
            data: values,
            backgroundColor: [
              '#FF6384', '#36A2EB', '#FFCE56', '#4BC0C0', '#9966FF', '#FF9F40'
            ],
            borderWidth: 1
          }]
        },
        options: {
          responsive: true,
          plugins: {
            legend: {
              position: 'top',
            },
            title: {
              display: true,
              text: '消费类型占比'
            }
          }
        }
      });
    } catch (error) {
      console.error('图表加载失败:', error);
      document.getElementById('expenseTypeChart').innerHTML = '<div class="chart-error">图表加载失败</div>';
    }
  });
</script>
    const ctx = document.getElementById('expenseTypeChart').getContext('2d');
    
    const expenseTypeChart = new Chart(ctx, {
      type: 'pie',
      data: {
        labels: ${expenseTypeLabels},
        datasets: [{
          label: '消费金额',
          data: ${expenseTypeValues},
          backgroundColor: [
            '#FF6384', '#36A2EB', '#FFCE56', '#4BC0C0', '#9966FF', '#FF9F40'
          ],
          borderWidth: 1
        }]
      },
      options: {
        responsive: true,
        plugins: {
          legend: {
            position: 'right',
          },
          title: {
            display: true,
            text: '消费类型占比'
          }
        }
      }
    });
  });
</script>
</body>
</html> 