package edu.wlxy.testspringboot1.controller;

import edu.wlxy.testspringboot1.model.TExpenseSummary;
import edu.wlxy.testspringboot1.service.TExpenseSummaryService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 消费账单控制器
 */
@RestController
@RequestMapping("/api/expenseSummary")
public class TExpenseSummaryController {
    
    @Autowired
    private TExpenseSummaryService expenseSummaryService;
    
    /**
     * 添加消费账单
     */
    @PostMapping("/add")
    public Map<String, Object> addExpenseSummary(@RequestBody TExpenseSummary expenseSummary) {
        Map<String, Object> result = new HashMap<>();
        try {
            int rows = expenseSummaryService.addExpenseSummary(expenseSummary);
            if (rows > 0) {
                result.put("success", true);
                result.put("message", "添加成功");
            } else {
                result.put("success", false);
                result.put("message", "添加失败");
            }
        } catch (Exception e) {
            result.put("success", false);
            result.put("message", "添加失败：" + e.getMessage());
        }
        return result;
    }
    
    /**
     * 删除消费账单
     */
    @DeleteMapping("/delete/{id}")
    public Map<String, Object> deleteExpenseSummary(@PathVariable("id") Integer id) {
        Map<String, Object> result = new HashMap<>();
        try {
            int rows = expenseSummaryService.deleteExpenseSummary(id);
            if (rows > 0) {
                result.put("success", true);
                result.put("message", "删除成功");
            } else {
                result.put("success", false);
                result.put("message", "删除失败，账单不存在");
            }
        } catch (Exception e) {
            result.put("success", false);
            result.put("message", "删除失败：" + e.getMessage());
        }
        return result;
    }
    
    /**
     * 更新消费账单
     */
    @PutMapping("/update")
    public Map<String, Object> updateExpenseSummary(@RequestBody TExpenseSummary expenseSummary) {
        Map<String, Object> result = new HashMap<>();
        try {
            int rows = expenseSummaryService.updateExpenseSummary(expenseSummary);
            if (rows > 0) {
                result.put("success", true);
                result.put("message", "更新成功");
            } else {
                result.put("success", false);
                result.put("message", "更新失败，账单不存在");
            }
        } catch (Exception e) {
            result.put("success", false);
            result.put("message", "更新失败：" + e.getMessage());
        }
        return result;
    }
    
    /**
     * 根据ID查询消费账单
     */
    @GetMapping("/get/{id}")
    public Map<String, Object> getExpenseSummary(@PathVariable("id") Integer id) {
        Map<String, Object> result = new HashMap<>();
        try {
            TExpenseSummary expenseSummary = expenseSummaryService.getExpenseSummaryById(id);
            if (expenseSummary != null) {
                result.put("success", true);
                result.put("data", expenseSummary);
            } else {
                result.put("success", false);
                result.put("message", "账单不存在");
            }
        } catch (Exception e) {
            result.put("success", false);
            result.put("message", "查询失败：" + e.getMessage());
        }
        return result;
    }
    
    /**
     * 条件查询消费账单
     */
    @PostMapping("/list")
    public Map<String, Object> getExpenseSummariesByCondition(@RequestBody(required = false) TExpenseSummary expenseSummary) {
        Map<String, Object> result = new HashMap<>();
        try {
            if (expenseSummary == null) {
                expenseSummary = new TExpenseSummary();
            }
            List<TExpenseSummary> expenseSummaries = expenseSummaryService.getExpenseSummariesByCondition(expenseSummary);
            result.put("success", true);
            result.put("data", expenseSummaries);
        } catch (Exception e) {
            result.put("success", false);
            result.put("message", "查询失败：" + e.getMessage());
        }
        return result;
    }
} 