package edu.wlxy.testspringboot1.model;

/**
 * 项目团队成员实体类
 */
public class TProjectTeam {
    private Integer id;
    private String projectId;
    private String memberId;
    private String memberRole;
    private Boolean isTeamLeader;
    private Boolean isProjectManager;
    private Boolean isRegularMember;
    private String createTime;

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getProjectId() {
        return projectId;
    }

    public void setProjectId(String projectId) {
        this.projectId = projectId;
    }

    public String getMemberId() {
        return memberId;
    }

    public void setMemberId(String memberId) {
        this.memberId = memberId;
    }

    public String getMemberRole() {
        return memberRole;
    }

    public void setMemberRole(String memberRole) {
        this.memberRole = memberRole;
    }

    public Boolean getIsTeamLeader() {
        return isTeamLeader;
    }

    public void setIsTeamLeader(Boolean isTeamLeader) {
        this.isTeamLeader = isTeamLeader;
    }

    public Boolean getIsProjectManager() {
        return isProjectManager;
    }

    public void setIsProjectManager(Boolean isProjectManager) {
        this.isProjectManager = isProjectManager;
    }

    public Boolean getIsRegularMember() {
        return isRegularMember;
    }

    public void setIsRegularMember(Boolean isRegularMember) {
        this.isRegularMember = isRegularMember;
    }

    public String getCreateTime() {
        return createTime;
    }

    public void setCreateTime(String createTime) {
        this.createTime = createTime;
    }
} 