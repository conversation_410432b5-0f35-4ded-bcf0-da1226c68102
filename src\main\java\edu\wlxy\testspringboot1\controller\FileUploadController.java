package edu.wlxy.testspringboot1.controller;

import edu.wlxy.testspringboot1.utils.ResultObject;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.multipart.MultipartFile;

import java.io.File;
import java.io.IOException;
import java.util.UUID;

@Controller
public class FileUploadController {

    @RequestMapping(value = "/upload")
    @ResponseBody
    public Object uploadFile(
            @RequestParam(value="file",required = false) MultipartFile file){

        System.out.println("请求了");
        ResultObject rs = new ResultObject();
        //在保存文件前对文件做检查
        //判断文件是否为空
        if(file.isEmpty()){
            rs.setFailureMsg("上传文件为空，上传失败");
            return rs;
        }
        //判断文件是否超过限制
        if(file.getSize() >Integer.valueOf(100000000)){
            rs.setFailureMsg("文件过大，上传失败");
            return rs;
        }

        //获取上传文件的原始名字
        String originalFilename = file.getOriginalFilename();

        //获取文件的后缀名
        String suffix = originalFilename.substring(originalFilename.lastIndexOf("."));

        //使用时间戳为文件定义新的名字
        String uuidName = UUID.randomUUID().toString().replace("-","");

        //为文件定义完整名字
        String fileName = uuidName + suffix;

        //定义文件保存位置
        String realPath = "C:/Users/<USER>/Pictures/Saved Pictures/" + fileName;

        //创建虚拟目标文件
        File destFile = new File(realPath);

        //获得上一级目录
        File parentFile = destFile.getParentFile();

        if(!parentFile.exists()){
            parentFile.mkdirs();
        }

        //向目标文件写入内容
        try {
            file.transferTo(destFile);
        }catch (IOException e) {
            rs.setFailureMsg("文件状态异常，写入失败");
            return rs;
        }

        rs.setSuccessMsgAndResult("文件上传成功",fileName);
        return rs;
    }
}
