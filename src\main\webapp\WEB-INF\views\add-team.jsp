<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>添加团队成员 - 旅游项目管理系统</title>
  <link rel="stylesheet" href="${pageContext.request.contextPath}/static/css/style.css">
  <link rel="stylesheet" href="${pageContext.request.contextPath}/static/fonts/fontawesome-free-6.4.0-web/css/all.min.css">
  <link rel="stylesheet" href="${pageContext.request.contextPath}/static/css/add-team.css">
</head>
<body>
<jsp:include page="common/nav.jsp" />

<div class="main-content">
  <div class="container">
    <div class="page-header">
      <div class="header-wrapper">
        <h2><i class="fa fa-users"></i> 添加团队成员</h2>
        <p class="subtitle">为项目添加团队成员</p>
      </div>
    </div>

    <div class="form-container">
      <form action="${pageContext.request.contextPath}/team/add" method="post">
        <div class="form-section">
          <h3><i class="fa fa-project-diagram"></i> 项目信息</h3>
          <div class="form-row">
            <div class="form-group">
              <label for="projectId">选择项目：</label>
              <select id="projectId" name="projectId" required>
                <option value="">--请选择项目--</option>
                <c:forEach items="${projects}" var="project">
                  <option value="${project.projectId}">${project.projectName}</option>
                </c:forEach>
              </select>
            </div>
          </div>
        </div>

        <div class="form-section">
          <h3><i class="fa fa-user-plus"></i> 成员信息</h3>
          <div class="form-row">
            <div class="form-group">
              <label for="memberId">选择成员：</label>
              <select id="memberId" name="memberId" required>
                <option value="">--请选择成员--</option>
                <c:forEach items="${users}" var="user">
                  <option value="${user.userId}">${user.username}</option>
                </c:forEach>
              </select>
            </div>
            <div class="form-group">
              <label for="memberRole">成员角色：</label>
              <input type="text" id="memberRole" name="memberRole" required>
            </div>
          </div>
          <div class="form-row">
            <div class="form-group">
              <label class="checkbox-label">
                <input type="checkbox" id="isTeamLeader" name="isTeamLeader" value="true">
                <span>团队领导</span>
              </label>
            </div>
            <div class="form-group">
              <label class="checkbox-label">
                <input type="checkbox" id="isProjectManager" name="isProjectManager" value="true">
                <span>项目经理</span>
              </label>
            </div>
            <div class="form-group">
              <label class="checkbox-label">
                <input type="checkbox" id="isRegularMember" name="isRegularMember" value="true" checked>
                <span>普通成员</span>
              </label>
            </div>
          </div>
        </div>

        <div class="form-actions">
          <button type="submit" class="btn-primary">
            <i class="fa fa-save"></i> 添加成员
          </button>
          <a href="${pageContext.request.contextPath}/team/list" class="btn-secondary">
            <i class="fa fa-times"></i> 取消
          </a>
        </div>
      </form>
    </div>

    <!-- 当前团队成员列表 -->
    <div class="team-list">
      <div class="list-header">
        <h3><i class="fa fa-list"></i> 当前团队成员</h3>
        <div class="filter-controls">
          <select id="projectFilter" onchange="filterTeamMembers()">
            <option value="">所有项目</option>
            <c:forEach items="${projects}" var="project">
              <option value="${project.projectId}">${project.projectName}</option>
            </c:forEach>
          </select>
        </div>
      </div>
      <div class="table-responsive">
        <table>
          <thead>
          <tr>
            <th>ID</th>
            <th>项目</th>
            <th>成员</th>
            <th>角色</th>
            <th>职位</th>
            <th>加入时间</th>
            <th>操作</th>
          </tr>
          </thead>
          <tbody id="teamMembersTable">
          <c:forEach items="${teamMembers}" var="member">
            <tr data-project-id="${member.projectId}">
              <td>${member.id}</td>
              <td>${member.projectName}</td>
              <td>${member.memberName}</td>
              <td>${member.memberRole}</td>
              <td>
                <c:if test="${member.isTeamLeader}"><span class="badge badge-leader">团队领导</span></c:if>
                <c:if test="${member.isProjectManager}"><span class="badge badge-manager">项目经理</span></c:if>
                <c:if test="${member.isRegularMember}"><span class="badge badge-member">普通成员</span></c:if>
              </td>
              <td>${member.createTime}</td>
              <td class="action-buttons">
                <a href="${pageContext.request.contextPath}/team/edit/${member.id}" class="btn-action" title="编辑">
                  <i class="fa fa-pencil"></i>
                </a>
                <a href="javascript:void(0)" onclick="deleteMember(${member.id})" class="btn-action" title="删除">
                  <i class="fa fa-trash"></i>
                </a>
              </td>
            </tr>
          </c:forEach>
          </tbody>
        </table>
      </div>
    </div>
  </div>
</div>

<script>
  function filterTeamMembers() {
    const projectId = document.getElementById('projectFilter').value;
    const rows = document.querySelectorAll('#teamMembersTable tr');
    
    rows.forEach(row => {
      if (!projectId || row.getAttribute('data-project-id') === projectId) {
        row.style.display = '';
      } else {
        row.style.display = 'none';
      }
    });
  }
  
  function deleteMember(id) {
    if (confirm('确定要删除此团队成员吗？')) {
      location.href = '${pageContext.request.contextPath}/team/delete/' + id;
    }
  }
  
  // 角色选择互斥控制
  document.addEventListener('DOMContentLoaded', function() {
    const isTeamLeader = document.getElementById('isTeamLeader');
    const isProjectManager = document.getElementById('isProjectManager');
    const isRegularMember = document.getElementById('isRegularMember');
    
    isTeamLeader.addEventListener('change', function() {
      if (this.checked) {
        isRegularMember.checked = false;
      }
    });
    
    isProjectManager.addEventListener('change', function() {
      if (this.checked) {
        isRegularMember.checked = false;
      }
    });
    
    isRegularMember.addEventListener('change', function() {
      if (this.checked) {
        isTeamLeader.checked = false;
        isProjectManager.checked = false;
      }
    });
  });
</script>
</body>
</html> 