package edu.wlxy.testspringboot1.controller;

import edu.wlxy.testspringboot1.model.TTravelProject;
import edu.wlxy.testspringboot1.service.TTravelProjectService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.mvc.support.RedirectAttributes;

import javax.servlet.http.HttpSession;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 旅游项目控制器
 */
@Controller
@RequestMapping("/project")
public class ProjectController {
    
    @Autowired
    private TTravelProjectService travelProjectService;
    
    /**
     * 首页
     */
    @GetMapping("/index")
    public String index(Model model, HttpSession session) {
        // 检查用户是否登录
        if (session.getAttribute("user") == null) {
            return "redirect:/user/toLogin";
        }
        
        // 获取所有项目
        TTravelProject query = new TTravelProject();
        List<TTravelProject> projects = travelProjectService.getTravelProjectsByCondition(query);
        
        // 统计各状态项目数量
        int completedCount = 0;
        int inProgressCount = 0;
        int plannedCount = 0;
        
        for (TTravelProject project : projects) {
            if ("已完成".equals(project.getProjectStatus())) {
                completedCount++;
            } else if ("进行中".equals(project.getProjectStatus())) {
                inProgressCount++;
            } else if ("计划中".equals(project.getProjectStatus())) {
                plannedCount++;
            }
        }
        
        // 分页信息（简化版，实际应该使用分页插件）
        int total = projects.size();
        int currentPage = 1;
        int pageSize = 10;
        int totalPages = (total + pageSize - 1) / pageSize;
        int startPage = Math.max(1, currentPage - 2);
        int endPage = Math.min(totalPages, currentPage + 2);
        
        model.addAttribute("projects", projects);
        model.addAttribute("completedCount", completedCount);
        model.addAttribute("inProgressCount", inProgressCount);
        model.addAttribute("plannedCount", plannedCount);
        model.addAttribute("total", total);
        model.addAttribute("currentPage", currentPage);
        model.addAttribute("totalPages", totalPages);
        model.addAttribute("startPage", startPage);
        model.addAttribute("endPage", endPage);
        
        return "index";
    }
    
    /**
     * 跳转到添加项目页面
     */
    @GetMapping("/toAdd")
    public String toAdd(HttpSession session) {
        // 检查用户是否登录
        if (session.getAttribute("user") == null) {
            return "redirect:/user/toLogin";
        }
        return "add-project";
    }
    
    /**
     * 添加项目
     */
    @PostMapping("/add")
    public String add(TTravelProject project, RedirectAttributes redirectAttributes) {
        // 设置创建时间
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        project.setCreateTime(sdf.format(new Date()));
        
        int result = travelProjectService.addTravelProject(project);
        
        if (result > 0) {
            redirectAttributes.addFlashAttribute("successMsg", "项目添加成功");
        } else {
            redirectAttributes.addFlashAttribute("errorMsg", "项目添加失败");
        }
        
        return "redirect:/project/index";
    }
    
    /**
     * 跳转到编辑项目页面
     */
    @GetMapping("/edit/{projectId}")
    public String toEdit(@PathVariable("projectId") String projectId, Model model, HttpSession session) {
        // 检查用户是否登录
        if (session.getAttribute("user") == null) {
            return "redirect:/user/toLogin";
        }
        
        TTravelProject project = travelProjectService.getTravelProjectById(projectId);
        if (project != null) {
            model.addAttribute("project", project);
            return "edit-project";
        } else {
            return "redirect:/project/index";
        }
    }
    
    /**
     * 更新项目
     */
    @PostMapping("/update")
    public String update(TTravelProject project, RedirectAttributes redirectAttributes) {
        int result = travelProjectService.updateTravelProject(project);
        
        if (result > 0) {
            redirectAttributes.addFlashAttribute("successMsg", "项目更新成功");
        } else {
            redirectAttributes.addFlashAttribute("errorMsg", "项目更新失败");
        }
        
        return "redirect:/project/index";
    }
    
    /**
     * 删除项目
     */
    @GetMapping("/delete/{projectId}")
    public String delete(@PathVariable("projectId") String projectId, RedirectAttributes redirectAttributes) {
        int result = travelProjectService.deleteTravelProject(projectId);
        
        if (result > 0) {
            redirectAttributes.addFlashAttribute("successMsg", "项目删除成功");
        } else {
            redirectAttributes.addFlashAttribute("errorMsg", "项目删除失败");
        }
        
        return "redirect:/project/index";
    }
    
    /**
     * 查看项目详情
     */
    @GetMapping("/view/{projectId}")
    public String view(@PathVariable("projectId") String projectId, Model model, HttpSession session) {
        // 检查用户是否登录
        if (session.getAttribute("user") == null) {
            return "redirect:/user/toLogin";
        }
        
        TTravelProject project = travelProjectService.getTravelProjectById(projectId);
        if (project != null) {
            model.addAttribute("project", project);
            return "view-project";
        } else {
            return "redirect:/project/index";
        }
    }
    
    /**
     * 项目搜索
     */
    @PostMapping("/list")
    public String list(TTravelProject query, @RequestParam(defaultValue = "1") int page, Model model, HttpSession session) {
        // 检查用户是否登录
        if (session.getAttribute("user") == null) {
            return "redirect:/user/toLogin";
        }
        
        // 查询符合条件的项目
        List<TTravelProject> projects = travelProjectService.getTravelProjectsByCondition(query);
        
        // 统计各状态项目数量
        int completedCount = 0;
        int inProgressCount = 0;
        int plannedCount = 0;
        
        for (TTravelProject project : projects) {
            if ("已完成".equals(project.getProjectStatus())) {
                completedCount++;
            } else if ("进行中".equals(project.getProjectStatus())) {
                inProgressCount++;
            } else if ("计划中".equals(project.getProjectStatus())) {
                plannedCount++;
            }
        }
        
        // 分页信息（简化版，实际应该使用分页插件）
        int total = projects.size();
        int pageSize = 10;
        int totalPages = (total + pageSize - 1) / pageSize;
        int startPage = Math.max(1, page - 2);
        int endPage = Math.min(totalPages, page + 2);
        
        model.addAttribute("projects", projects);
        model.addAttribute("completedCount", completedCount);
        model.addAttribute("inProgressCount", inProgressCount);
        model.addAttribute("plannedCount", plannedCount);
        model.addAttribute("total", total);
        model.addAttribute("currentPage", page);
        model.addAttribute("totalPages", totalPages);
        model.addAttribute("startPage", startPage);
        model.addAttribute("endPage", endPage);
        
        return "index";
    }
    
    /**
     * 导出项目
     */
    @GetMapping("/export")
    @ResponseBody
    public Map<String, Object> export() {
        Map<String, Object> result = new HashMap<>();
        result.put("success", true);
        result.put("message", "导出功能尚未实现");
        return result;
    }
} 