package edu.wlxy.testspringboot1.service.impl;

import edu.wlxy.testspringboot1.mapper.TTravelProjectMapper;
import edu.wlxy.testspringboot1.model.TTravelProject;
import edu.wlxy.testspringboot1.service.TTravelProjectService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 旅游项目服务实现类
 */
@Service
public class TTravelProjectServiceImpl implements TTravelProjectService {
    
    @Autowired
    private TTravelProjectMapper travelProjectMapper;
    
    @Override
    public int addTravelProject(TTravelProject travelProject) {
        return travelProjectMapper.insertSelective(travelProject);
    }
    
    @Override
    public int deleteTravelProject(String projectId) {
        return travelProjectMapper.deleteByPrimaryKey(projectId);
    }
    
    @Override
    public int updateTravelProject(TTravelProject travelProject) {
        return travelProjectMapper.updateByPrimaryKeySelective(travelProject);
    }
    
    @Override
    public TTravelProject getTravelProjectById(String projectId) {
        return travelProjectMapper.selectByPrimaryKey(projectId);
    }
    
    @Override
    public List<TTravelProject> getTravelProjectsByCondition(TTravelProject travelProject) {
        return travelProjectMapper.selectBySelective(travelProject);
    }
} 