<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>编辑旅游项目 - 旅游项目管理系统</title>
  <link rel="stylesheet" href="${pageContext.request.contextPath}/static/css/style.css">
  <link rel="stylesheet" href="${pageContext.request.contextPath}/static/fonts/fontawesome-free-6.4.0-web/css/all.min.css">
  <link rel="stylesheet" href="${pageContext.request.contextPath}/static/css/edit-project.css">
</head>
<body>
<jsp:include page="common/nav.jsp" />

<div class="main-content">
  <div class="container">
    <div class="page-header">
      <div class="header-wrapper">
        <h2><i class="fa fa-edit"></i> 编辑旅游项目</h2>
        <p class="subtitle">修改项目：${project.projectName}</p>
      </div>
    </div>

    <div class="form-container">
      <form action="${pageContext.request.contextPath}/project/update" method="post">
        <input type="hidden" name="projectId" value="${project.projectId}">
        
        <div class="form-section">
          <h3><i class="fa fa-info-circle"></i> 基本信息</h3>
          <div class="form-row">
            <div class="form-group">
              <label for="projectId">项目ID：</label>
              <input type="text" id="projectId" value="${project.projectId}" disabled>
            </div>
            <div class="form-group">
              <label for="projectName">项目名称：</label>
              <input type="text" id="projectName" name="projectName" value="${project.projectName}" required>
            </div>
          </div>
          <div class="form-row">
            <div class="form-group">
              <label for="travelDestination">旅游目的地：</label>
              <input type="text" id="travelDestination" name="travelDestination" value="${project.travelDestination}" required>
            </div>
            <div class="form-group">
              <label for="startDate">开始日期：</label>
              <input type="date" id="startDate" name="startDate" value="${project.startDate}" required>
            </div>
          </div>
        </div>

        <div class="form-section">
          <h3><i class="fa fa-file-alt"></i> 项目详情</h3>
          <div class="form-row">
            <div class="form-group full-width">
              <label for="projectDetails">项目详情：</label>
              <textarea id="projectDetails" name="projectDetails" rows="4">${project.projectDetails}</textarea>
            </div>
          </div>
          <div class="form-row">
            <div class="form-group">
              <label for="projectStatus">项目状态：</label>
              <select id="projectStatus" name="projectStatus" required>
                <option value="计划中" ${project.projectStatus == '计划中' ? 'selected' : ''}>计划中</option>
                <option value="进行中" ${project.projectStatus == '进行中' ? 'selected' : ''}>进行中</option>
                <option value="已完成" ${project.projectStatus == '已完成' ? 'selected' : ''}>已完成</option>
              </select>
            </div>
          </div>
        </div>

        <div class="form-section">
          <h3><i class="fa fa-sticky-note"></i> 旅行笔记</h3>
          <div class="form-row">
            <div class="form-group full-width">
              <label for="travelNotes">旅行笔记：</label>
              <textarea id="travelNotes" name="travelNotes" rows="4">${project.travelNotes}</textarea>
            </div>
          </div>
        </div>

        <div class="form-actions">
          <button type="submit" class="btn-primary">
            <i class="fa fa-save"></i> 保存修改
          </button>
          <a href="${pageContext.request.contextPath}/project/index" class="btn-secondary">
            <i class="fa fa-times"></i> 取消
          </a>
        </div>
      </form>
    </div>
  </div>
</div>
</body>
</html> 