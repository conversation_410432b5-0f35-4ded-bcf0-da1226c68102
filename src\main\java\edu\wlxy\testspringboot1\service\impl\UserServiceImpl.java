package edu.wlxy.testspringboot1.service.impl;

import edu.wlxy.testspringboot1.mapper.UserMapper;
import edu.wlxy.testspringboot1.model.User;
import edu.wlxy.testspringboot1.service.UserService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 用户服务实现类
 */
@Service
public class UserServiceImpl implements UserService {
    
    @Autowired
    private UserMapper userMapper;
    
    @Override
    public int addUser(User user) {
        return userMapper.insert(user);
    }
    
    @Override
    public int deleteUser(String userId) {
        return userMapper.deleteById(userId);
    }
    
    @Override
    public int updateUser(User user) {
        return userMapper.update(user);
    }
    
    @Override
    public User getUserById(String userId) {
        return userMapper.findById(userId);
    }
    
    @Override
    public User getUserByUsername(String username) {
        return userMapper.findByUsername(username);
    }
    
    @Override
    public List<User> getAllUsers() {
        return userMapper.findAll();
    }
} 