package edu.wlxy.testspringboot1.controller;

import edu.wlxy.testspringboot1.model.TUser;
import edu.wlxy.testspringboot1.service.TUserService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 用户控制器
 */
@RestController
@RequestMapping("/api/user")
public class TUserController {
    
    @Autowired
    private TUserService userService;
    
    /**
     * 添加用户
     */
    @PostMapping("/add")
    public Map<String, Object> addUser(@RequestBody TUser user) {
        Map<String, Object> result = new HashMap<>();
        try {
            int rows = userService.addUser(user);
            if (rows > 0) {
                result.put("success", true);
                result.put("message", "添加成功");
            } else {
                result.put("success", false);
                result.put("message", "添加失败");
            }
        } catch (Exception e) {
            result.put("success", false);
            result.put("message", "添加失败：" + e.getMessage());
        }
        return result;
    }
    
    /**
     * 删除用户
     */
    @DeleteMapping("/delete/{userId}")
    public Map<String, Object> deleteUser(@PathVariable("userId") String userId) {
        Map<String, Object> result = new HashMap<>();
        try {
            int rows = userService.deleteUser(userId);
            if (rows > 0) {
                result.put("success", true);
                result.put("message", "删除成功");
            } else {
                result.put("success", false);
                result.put("message", "删除失败，用户不存在");
            }
        } catch (Exception e) {
            result.put("success", false);
            result.put("message", "删除失败：" + e.getMessage());
        }
        return result;
    }
    
    /**
     * 更新用户
     */
    @PutMapping("/update")
    public Map<String, Object> updateUser(@RequestBody TUser user) {
        Map<String, Object> result = new HashMap<>();
        try {
            int rows = userService.updateUser(user);
            if (rows > 0) {
                result.put("success", true);
                result.put("message", "更新成功");
            } else {
                result.put("success", false);
                result.put("message", "更新失败，用户不存在");
            }
        } catch (Exception e) {
            result.put("success", false);
            result.put("message", "更新失败：" + e.getMessage());
        }
        return result;
    }
    
    /**
     * 根据ID查询用户
     */
    @GetMapping("/get/{userId}")
    public Map<String, Object> getUser(@PathVariable("userId") String userId) {
        Map<String, Object> result = new HashMap<>();
        try {
            TUser user = userService.getUserById(userId);
            if (user != null) {
                result.put("success", true);
                result.put("data", user);
            } else {
                result.put("success", false);
                result.put("message", "用户不存在");
            }
        } catch (Exception e) {
            result.put("success", false);
            result.put("message", "查询失败：" + e.getMessage());
        }
        return result;
    }
    
    /**
     * 条件查询用户
     */
    @PostMapping("/list")
    public Map<String, Object> getUsersByCondition(@RequestBody(required = false) TUser user) {
        Map<String, Object> result = new HashMap<>();
        try {
            if (user == null) {
                user = new TUser();
            }
            List<TUser> users = userService.getUsersByCondition(user);
            result.put("success", true);
            result.put("data", users);
        } catch (Exception e) {
            result.put("success", false);
            result.put("message", "查询失败：" + e.getMessage());
        }
        return result;
    }
} 