package edu.wlxy.testspringboot1.service;

import edu.wlxy.testspringboot1.model.TTravelProject;

import java.util.List;

/**
 * 旅游项目服务接口
 */
public interface TTravelProjectService {
    
    /**
     * 新增旅游项目
     */
    int addTravelProject(TTravelProject travelProject);
    
    /**
     * 删除旅游项目
     */
    int deleteTravelProject(String projectId);
    
    /**
     * 更新旅游项目
     */
    int updateTravelProject(TTravelProject travelProject);
    
    /**
     * 根据ID查询旅游项目
     */
    TTravelProject getTravelProjectById(String projectId);
    
    /**
     * 条件查询旅游项目
     */
    List<TTravelProject> getTravelProjectsByCondition(TTravelProject travelProject);
} 