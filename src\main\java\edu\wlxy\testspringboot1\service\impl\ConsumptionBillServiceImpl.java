package edu.wlxy.testspringboot1.service.impl;

import edu.wlxy.testspringboot1.mapper.ConsumptionBillMapper;
import edu.wlxy.testspringboot1.model.ConsumptionBill;
import edu.wlxy.testspringboot1.service.ConsumptionBillService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 消费账单服务实现类
 */
@Service
public class ConsumptionBillServiceImpl implements ConsumptionBillService {
    
    @Autowired
    private ConsumptionBillMapper consumptionBillMapper;
    
    @Override
    public int addConsumptionBill(ConsumptionBill consumptionBill) {
        return consumptionBillMapper.insert(consumptionBill);
    }
    
    @Override
    public int deleteConsumptionBill(Integer id) {
        return consumptionBillMapper.deleteById(id);
    }
    
    @Override
    public int updateConsumptionBill(ConsumptionBill consumptionBill) {
        return consumptionBillMapper.update(consumptionBill);
    }
    
    @Override
    public ConsumptionBill getConsumptionBillById(Integer id) {
        return consumptionBillMapper.findById(id);
    }
    
    @Override
    public List<ConsumptionBill> getConsumptionBillsByProjectId(String projectId) {
        return consumptionBillMapper.findByProjectId(projectId);
    }
    
    @Override
    public List<ConsumptionBill> getAllConsumptionBills() {
        return consumptionBillMapper.findAll();
    }
} 