package edu.wlxy.testspringboot1.controller;

import edu.wlxy.testspringboot1.model.TTravelProject;
import edu.wlxy.testspringboot1.service.TTravelProjectService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 旅游项目控制器
 */
@RestController
@RequestMapping("/api/travelProject")
public class TTravelProjectController {
    
    @Autowired
    private TTravelProjectService travelProjectService;
    
    /**
     * 添加旅游项目
     */
    @PostMapping("/add")
    public Map<String, Object> addTravelProject(@RequestBody TTravelProject travelProject) {
        Map<String, Object> result = new HashMap<>();
        try {
            int rows = travelProjectService.addTravelProject(travelProject);
            if (rows > 0) {
                result.put("success", true);
                result.put("message", "添加成功");
            } else {
                result.put("success", false);
                result.put("message", "添加失败");
            }
        } catch (Exception e) {
            result.put("success", false);
            result.put("message", "添加失败：" + e.getMessage());
        }
        return result;
    }
    
    /**
     * 删除旅游项目
     */
    @DeleteMapping("/delete/{projectId}")
    public Map<String, Object> deleteTravelProject(@PathVariable("projectId") String projectId) {
        Map<String, Object> result = new HashMap<>();
        try {
            int rows = travelProjectService.deleteTravelProject(projectId);
            if (rows > 0) {
                result.put("success", true);
                result.put("message", "删除成功");
            } else {
                result.put("success", false);
                result.put("message", "删除失败，项目不存在");
            }
        } catch (Exception e) {
            result.put("success", false);
            result.put("message", "删除失败：" + e.getMessage());
        }
        return result;
    }
    
    /**
     * 更新旅游项目
     */
    @PutMapping("/update")
    public Map<String, Object> updateTravelProject(@RequestBody TTravelProject travelProject) {
        Map<String, Object> result = new HashMap<>();
        try {
            int rows = travelProjectService.updateTravelProject(travelProject);
            if (rows > 0) {
                result.put("success", true);
                result.put("message", "更新成功");
            } else {
                result.put("success", false);
                result.put("message", "更新失败，项目不存在");
            }
        } catch (Exception e) {
            result.put("success", false);
            result.put("message", "更新失败：" + e.getMessage());
        }
        return result;
    }
    
    /**
     * 根据ID查询旅游项目
     */
    @GetMapping("/get/{projectId}")
    public Map<String, Object> getTravelProject(@PathVariable("projectId") String projectId) {
        Map<String, Object> result = new HashMap<>();
        try {
            TTravelProject travelProject = travelProjectService.getTravelProjectById(projectId);
            if (travelProject != null) {
                result.put("success", true);
                result.put("data", travelProject);
            } else {
                result.put("success", false);
                result.put("message", "项目不存在");
            }
        } catch (Exception e) {
            result.put("success", false);
            result.put("message", "查询失败：" + e.getMessage());
        }
        return result;
    }
    
    /**
     * 条件查询旅游项目
     */
    @PostMapping("/list")
    public Map<String, Object> getTravelProjectsByCondition(@RequestBody(required = false) TTravelProject travelProject) {
        Map<String, Object> result = new HashMap<>();
        try {
            if (travelProject == null) {
                travelProject = new TTravelProject();
            }
            List<TTravelProject> travelProjects = travelProjectService.getTravelProjectsByCondition(travelProject);
            result.put("success", true);
            result.put("data", travelProjects);
        } catch (Exception e) {
            result.put("success", false);
            result.put("message", "查询失败：" + e.getMessage());
        }
        return result;
    }
} 