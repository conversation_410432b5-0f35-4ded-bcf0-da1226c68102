package edu.wlxy.testspringboot1.mapper;

import edu.wlxy.testspringboot1.model.TExpenseSummary;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * 消费账单DAO接口
 */
@Mapper
public interface TExpenseSummaryMapper {
    
    /**
     * 根据ID删除消费账单
     */
    int deleteByPrimaryKey(Integer id);
    
    /**
     * 新增消费账单
     */
    int insertSelective(TExpenseSummary record);
    
    /**
     * 根据ID查询消费账单
     */
    TExpenseSummary selectByPrimaryKey(Integer id);
    
    /**
     * 更新消费账单
     */
    int updateByPrimaryKeySelective(TExpenseSummary record);
    
    /**
     * 条件查询消费账单
     */
    List<TExpenseSummary> selectBySelective(TExpenseSummary record);
} 