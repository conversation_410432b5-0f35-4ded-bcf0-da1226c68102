package edu.wlxy.testspringboot1.service;

import edu.wlxy.testspringboot1.model.TProjectTeam;

import java.util.List;

/**
 * 项目团队成员服务接口
 */
public interface TProjectTeamService {
    
    /**
     * 新增项目团队成员
     */
    int addProjectTeam(TProjectTeam projectTeam);
    
    /**
     * 删除项目团队成员
     */
    int deleteProjectTeam(Integer id);
    
    /**
     * 更新项目团队成员
     */
    int updateProjectTeam(TProjectTeam projectTeam);
    
    /**
     * 根据ID查询项目团队成员
     */
    TProjectTeam getProjectTeamById(Integer id);
    
    /**
     * 条件查询项目团队成员
     */
    List<TProjectTeam> getProjectTeamsByCondition(TProjectTeam projectTeam);
} 