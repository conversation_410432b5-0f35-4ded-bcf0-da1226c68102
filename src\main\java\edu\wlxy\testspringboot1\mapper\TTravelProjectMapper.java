package edu.wlxy.testspringboot1.mapper;

import edu.wlxy.testspringboot1.model.TTravelProject;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * 旅游项目DAO接口
 */
@Mapper
public interface TTravelProjectMapper {
    
    /**
     * 根据ID删除旅游项目
     */
    int deleteByPrimaryKey(String projectId);
    
    /**
     * 新增旅游项目
     */
    int insertSelective(TTravelProject record);
    
    /**
     * 根据ID查询旅游项目
     */
    TTravelProject selectByPrimaryKey(String projectId);
    
    /**
     * 更新旅游项目
     */
    int updateByPrimaryKeySelective(TTravelProject record);
    
    /**
     * 条件查询旅游项目
     */
    List<TTravelProject> selectBySelective(TTravelProject record);
} 