package edu.wlxy.testspringboot1.service.impl;

import edu.wlxy.testspringboot1.mapper.TExpenseSummaryMapper;
import edu.wlxy.testspringboot1.model.TExpenseSummary;
import edu.wlxy.testspringboot1.service.TExpenseSummaryService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 消费账单服务实现类
 */
@Service
public class TExpenseSummaryServiceImpl implements TExpenseSummaryService {
    
    @Autowired
    private TExpenseSummaryMapper expenseSummaryMapper;
    
    @Override
    public int addExpenseSummary(TExpenseSummary expenseSummary) {
        return expenseSummaryMapper.insertSelective(expenseSummary);
    }
    
    @Override
    public int deleteExpenseSummary(Integer id) {
        return expenseSummaryMapper.deleteByPrimaryKey(id);
    }
    
    @Override
    public int updateExpenseSummary(TExpenseSummary expenseSummary) {
        return expenseSummaryMapper.updateByPrimaryKeySelective(expenseSummary);
    }
    
    @Override
    public TExpenseSummary getExpenseSummaryById(Integer id) {
        return expenseSummaryMapper.selectByPrimaryKey(id);
    }
    
    @Override
    public List<TExpenseSummary> getExpenseSummariesByCondition(TExpenseSummary expenseSummary) {
        return expenseSummaryMapper.selectBySelective(expenseSummary);
    }
} 