<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>首页 - 旅游项目管理系统</title>
  <link rel="stylesheet" href="${pageContext.request.contextPath}/static/css/style.css">
  <link rel="stylesheet" href="${pageContext.request.contextPath}/static/fonts/fontawesome-free-6.4.0-web/css/all.min.css">
  <link rel="stylesheet" href="${pageContext.request.contextPath}/static/css/index.css">
</head>
<body>
<jsp:include page="common/nav.jsp" />

<div class="main-content">
  <div class="container">
    <div class="page-header">
      <div class="header-wrapper">
        <h2><i class="fa fa-edit"></i> 旅游项目管理系统</h2>
        <p class="subtitle">欢迎回来，<span id="username">${sessionScope.user.username}</span></p>
      </div>
      <div class="quick-stats">
        <div class="stat-item">
          <i class="fa fa-check-circle"></i>
          <div class="stat-info">
            <span class="stat-value">${completedCount}</span>
            <span class="stat-label">已完成</span>
          </div>
        </div>
        <div class="stat-item">
          <i class="fa fa-spinner"></i>
          <div class="stat-info">
            <span class="stat-value">${inProgressCount}</span>
            <span class="stat-label">进行中</span>
          </div>
        </div>
        <div class="stat-item">
          <i class="fa fa-clock"></i>
          <div class="stat-info">
            <span class="stat-value">${plannedCount}</span>
            <span class="stat-label">计划中</span>
          </div>
        </div>
      </div>
    </div>

    <!-- 搜索条件区域 -->
    <div class="search-area">
      <div class="search-header">
        <h3><i class="fa fa-search"></i> 搜索条件</h3>
        <button class="btn-add" onclick="location.href='${pageContext.request.contextPath}/project/toAdd'">
          <i class="fa fa-plus"></i> 新建项目
        </button>
      </div>
      <div class="search-content">
        <form id="searchForm" action="${pageContext.request.contextPath}/project/list" method="post">
          <div class="search-row">
            <div class="search-item">
              <label><i class="fa fa-hashtag"></i> 项目名称</label>
              <input type="text" name="projectName" placeholder="请输入项目名称" class="form-control" value="${param.projectName}">
            </div>
            <div class="search-item">
              <label><i class="fa fa-tag"></i> 项目状态</label>
              <select name="projectStatus" class="form-control">
                <option value="">全部</option>
                <option value="已完成" ${param.projectStatus == '已完成' ? 'selected' : ''}>已完成</option>
                <option value="进行中" ${param.projectStatus == '进行中' ? 'selected' : ''}>进行中</option>
                <option value="计划中" ${param.projectStatus == '计划中' ? 'selected' : ''}>计划中</option>
              </select>
            </div>
            <div class="search-item">
              <label><i class="fa fa-calendar"></i> 年份</label>
              <select name="year" class="form-control">
                <option value="">全部</option>
                <option value="2024" ${param.year == '2024' ? 'selected' : ''}>2024</option>
                <option value="2025" ${param.year == '2025' ? 'selected' : ''}>2025</option>
              </select>
            </div>
          </div>
          <div class="search-row">
            <div class="search-item">
              <label><i class="fa fa-map-marker"></i> 地点</label>
              <input type="text" name="travelDestination" placeholder="请输入地点" class="form-control" value="${param.travelDestination}">
            </div>
            <div class="search-actions">
              <button type="submit" class="btn-search">
                <i class="fa fa-search"></i> 查询
              </button>
              <button type="reset" class="btn-reset">
                <i class="fa fa-refresh"></i> 重置
              </button>
            </div>
          </div>
        </form>
      </div>
    </div>

    <!-- 项目列表 -->
    <div class="project-list">
      <div class="list-header">
        <h3><i class="fa fa-list"></i> 项目列表</h3>
        <div class="list-actions">
          <button class="btn-export" onclick="exportProjects()">
            <i class="fa fa-download"></i> 导出
          </button>
          <button class="btn-batch" onclick="showBatchOptions()">
            <i class="fa fa-cog"></i> 批量操作
          </button>
        </div>
      </div>
      <div class="table-responsive">
        <table>
          <thead>
          <tr>
            <th><input type="checkbox" class="select-all" onclick="toggleSelectAll(this)"></th>
            <th>项目ID</th>
            <th>项目名称</th>
            <th>地点</th>
            <th>开始时间</th>
            <th>备注</th>
            <th>状态</th>
            <th>操作</th>
          </tr>
          </thead>
          <tbody>
          <c:forEach items="${projects}" var="project">
            <tr>
              <td><input type="checkbox" class="select-item" value="${project.projectId}"></td>
              <td>${project.projectId}</td>
              <td class="project-name">
                <i class="fa fa-bookmark"></i>
                <span>${project.projectName}</span>
              </td>
              <td><i class="fa fa-map-marker"></i> ${project.travelDestination}</td>
              <td>${project.startDate}</td>
              <td class="project-notes">${project.projectDetails}</td>
              <td><span class="status-badge status-${project.projectStatus == '已完成' ? 'completed' : project.projectStatus == '进行中' ? 'progress' : 'planned'}">${project.projectStatus}</span></td>
              <td class="action-buttons">
                <a href="${pageContext.request.contextPath}/project/view/${project.projectId}" class="btn-action" title="查看详情">
                  <i class="fa fa-eye"></i>
                </a>
                <a href="${pageContext.request.contextPath}/project/edit/${project.projectId}" class="btn-action" title="编辑">
                  <i class="fa fa-pencil"></i>
                </a>
                <a href="javascript:void(0)" onclick="deleteProject('${project.projectId}')" class="btn-action" title="删除">
                  <i class="fa fa-trash"></i>
                </a>
              </td>
            </tr>
          </c:forEach>
          </tbody>
        </table>
      </div>

      <!-- 分页 -->
      <div class="pagination">
        <span class="page-info">共 ${total} 条记录</span>
        <div class="page-buttons">
          <c:if test="${currentPage > 1}">
            <a href="javascript:void(0)" onclick="goToPage(1)" class="btn-page" title="首页">
              <i class="fa fa-angle-double-left"></i>
            </a>
            <a href="javascript:void(0)" onclick="goToPage(${currentPage - 1})" class="btn-page" title="上一页">
              <i class="fa fa-angle-left"></i>
            </a>
          </c:if>
          
          <c:forEach begin="${startPage}" end="${endPage}" var="i">
            <a href="javascript:void(0)" onclick="goToPage(${i})" class="btn-page ${i == currentPage ? 'active' : ''}">${i}</a>
          </c:forEach>
          
          <c:if test="${currentPage < totalPages}">
            <a href="javascript:void(0)" onclick="goToPage(${currentPage + 1})" class="btn-page" title="下一页">
              <i class="fa fa-angle-right"></i>
            </a>
            <a href="javascript:void(0)" onclick="goToPage(${totalPages})" class="btn-page" title="末页">
              <i class="fa fa-angle-double-right"></i>
            </a>
          </c:if>
        </div>
      </div>
    </div>
  </div>
</div>

<script>
  function goToPage(page) {
    const form = document.getElementById('searchForm');
    const pageInput = document.createElement('input');
    pageInput.type = 'hidden';
    pageInput.name = 'page';
    pageInput.value = page;
    form.appendChild(pageInput);
    form.submit();
  }
  
  function toggleSelectAll(checkbox) {
    const items = document.getElementsByClassName('select-item');
    for (let i = 0; i < items.length; i++) {
      items[i].checked = checkbox.checked;
    }
  }
  
  function deleteProject(projectId) {
    if (confirm('确定要删除此项目吗？')) {
      location.href = '${pageContext.request.contextPath}/project/delete/' + projectId;
    }
  }
  
  function exportProjects() {
    window.location.href = '${pageContext.request.contextPath}/project/export';
  }
  
  function showBatchOptions() {
    const selected = document.querySelectorAll('.select-item:checked');
    if (selected.length === 0) {
      alert('请先选择要操作的项目');
      return;
    }
    
    const ids = Array.from(selected).map(item => item.value).join(',');
    // 显示批量操作选项，可以是弹窗或下拉菜单
    alert('选中的项目ID: ' + ids + '\n可以进行批量删除、批量更新状态等操作');
  }
</script>
</body>
</html> 