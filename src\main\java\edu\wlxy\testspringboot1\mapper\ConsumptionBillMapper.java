package edu.wlxy.testspringboot1.mapper;

import edu.wlxy.testspringboot1.model.ConsumptionBill;
import org.apache.ibatis.annotations.*;

import java.util.List;

/**
 * 消费账单DAO接口
 */
@Mapper
public interface ConsumptionBillMapper {
    
    /**
     * 新增消费账单
     */
    @Insert("INSERT INTO expensesummary(projectid, expensetype, expenseamount, price, quantity, expensedate, expensenotes) " +
            "VALUES(#{projectId}, #{expenseType}, #{expenseAmount}, #{price}, #{quantity}, #{expenseDate}, #{expenseNotes})")
    @Options(useGeneratedKeys = true, keyProperty = "id")
    int insert(ConsumptionBill consumptionBill);
    
    /**
     * 删除消费账单
     */
    @Delete("DELETE FROM expensesummary WHERE id = #{id}")
    int deleteById(Integer id);
    
    /**
     * 更新消费账单
     */
    @Update("UPDATE expensesummary SET projectid = #{projectId}, expensetype = #{expenseType}, " +
            "expenseamount = #{expenseAmount}, price = #{price}, quantity = #{quantity}, " +
            "expensedate = #{expenseDate}, expensenotes = #{expenseNotes} WHERE id = #{id}")
    int update(ConsumptionBill consumptionBill);
    
    /**
     * 根据ID查询消费账单
     */
    @Select("SELECT id, projectid, expensetype, expenseamount, price, quantity, expensedate, expensenotes " +
            "FROM expensesummary WHERE id = #{id}")
    @Results({
        @Result(column = "id", property = "id"),
        @Result(column = "projectid", property = "projectId"),
        @Result(column = "expensetype", property = "expenseType"),
        @Result(column = "expenseamount", property = "expenseAmount"),
        @Result(column = "price", property = "price"),
        @Result(column = "quantity", property = "quantity"),
        @Result(column = "expensedate", property = "expenseDate"),
        @Result(column = "expensenotes", property = "expenseNotes")
    })
    ConsumptionBill findById(Integer id);
    
    /**
     * 根据项目ID查询消费账单
     */
    @Select("SELECT id, projectid, expensetype, expenseamount, price, quantity, expensedate, expensenotes " +
            "FROM expensesummary WHERE projectid = #{projectId}")
    @Results({
        @Result(column = "id", property = "id"),
        @Result(column = "projectid", property = "projectId"),
        @Result(column = "expensetype", property = "expenseType"),
        @Result(column = "expenseamount", property = "expenseAmount"),
        @Result(column = "price", property = "price"),
        @Result(column = "quantity", property = "quantity"),
        @Result(column = "expensedate", property = "expenseDate"),
        @Result(column = "expensenotes", property = "expenseNotes")
    })
    List<ConsumptionBill> findByProjectId(String projectId);
    
    /**
     * 查询所有消费账单
     */
    @Select("SELECT id, projectid, expensetype, expenseamount, price, quantity, expensedate, expensenotes FROM expensesummary")
    @Results({
        @Result(column = "id", property = "id"),
        @Result(column = "projectid", property = "projectId"),
        @Result(column = "expensetype", property = "expenseType"),
        @Result(column = "expenseamount", property = "expenseAmount"),
        @Result(column = "price", property = "price"),
        @Result(column = "quantity", property = "quantity"),
        @Result(column = "expensedate", property = "expenseDate"),
        @Result(column = "expensenotes", property = "expenseNotes")
    })
    List<ConsumptionBill> findAll();
} 