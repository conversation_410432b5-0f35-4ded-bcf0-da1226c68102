package edu.wlxy.testspringboot1.service.impl;

import edu.wlxy.testspringboot1.mapper.TourProjectMapper;
import edu.wlxy.testspringboot1.model.TourProject;
import edu.wlxy.testspringboot1.service.TourProjectService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 旅游项目服务实现类
 */
@Service
public class TourProjectServiceImpl implements TourProjectService {
    
    @Autowired
    private TourProjectMapper tourProjectMapper;
    
    @Override
    public int addTourProject(TourProject tourProject) {
        return tourProjectMapper.insert(tourProject);
    }
    
    @Override
    public int deleteTourProject(String projectId) {
        return tourProjectMapper.deleteById(projectId);
    }
    
    @Override
    public int updateTourProject(TourProject tourProject) {
        return tourProjectMapper.update(tourProject);
    }
    
    @Override
    public TourProject getTourProjectById(String projectId) {
        return tourProjectMapper.findById(projectId);
    }
    
    @Override
    public List<TourProject> getAllTourProjects() {
        return tourProjectMapper.findAll();
    }
} 