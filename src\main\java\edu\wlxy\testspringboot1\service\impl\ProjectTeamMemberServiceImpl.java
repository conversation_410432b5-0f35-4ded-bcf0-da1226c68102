package edu.wlxy.testspringboot1.service.impl;

import edu.wlxy.testspringboot1.mapper.ProjectTeamMemberMapper;
import edu.wlxy.testspringboot1.model.ProjectTeamMember;
import edu.wlxy.testspringboot1.service.ProjectTeamMemberService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 项目团队成员服务实现类
 */
@Service
public class ProjectTeamMemberServiceImpl implements ProjectTeamMemberService {
    
    @Autowired
    private ProjectTeamMemberMapper projectTeamMemberMapper;
    
    @Override
    public int addProjectTeamMember(ProjectTeamMember projectTeamMember) {
        return projectTeamMemberMapper.insert(projectTeamMember);
    }
    
    @Override
    public int deleteProjectTeamMember(Integer id) {
        return projectTeamMemberMapper.deleteById(id);
    }
    
    @Override
    public int updateProjectTeamMember(ProjectTeamMember projectTeamMember) {
        return projectTeamMemberMapper.update(projectTeamMember);
    }
    
    @Override
    public ProjectTeamMember getProjectTeamMemberById(Integer id) {
        return projectTeamMemberMapper.findById(id);
    }
    
    @Override
    public List<ProjectTeamMember> getProjectTeamMembersByProjectId(String projectId) {
        return projectTeamMemberMapper.findByProjectId(projectId);
    }
    
    @Override
    public List<ProjectTeamMember> getAllProjectTeamMembers() {
        return projectTeamMemberMapper.findAll();
    }
} 