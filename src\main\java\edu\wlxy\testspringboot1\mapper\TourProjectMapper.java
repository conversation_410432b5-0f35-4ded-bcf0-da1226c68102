package edu.wlxy.testspringboot1.mapper;

import edu.wlxy.testspringboot1.model.TourProject;
import org.apache.ibatis.annotations.*;

import java.util.List;

/**
 * 旅游项目DAO接口
 */
@Mapper
public interface TourProjectMapper {
    
    /**
     * 新增旅游项目
     */
    @Insert("INSERT INTO travelproject(projectid, projectname, createtime, traveldestination, startdate, projectdetails, projectstatus, travelnotes) " +
            "VALUES(#{projectId}, #{projectName}, #{createTime}, #{travelDestination}, #{startDate}, #{projectDetails}, #{projectStatus}, #{travelNotes})")
    int insert(TourProject tourProject);
    
    /**
     * 删除旅游项目
     */
    @Delete("DELETE FROM travelproject WHERE projectid = #{projectId}")
    int deleteById(String projectId);
    
    /**
     * 更新旅游项目
     */
    @Update("UPDATE travelproject SET projectname = #{projectName}, traveldestination = #{travelDestination}, " +
            "startdate = #{startDate}, projectdetails = #{projectDetails}, projectstatus = #{projectStatus}, " +
            "travelnotes = #{travelNotes} WHERE projectid = #{projectId}")
    int update(TourProject tourProject);
    
    /**
     * 根据ID查询旅游项目
     */
    @Select("SELECT projectid, projectname, createtime, traveldestination, startdate, projectdetails, projectstatus, travelnotes " +
            "FROM travelproject WHERE projectid = #{projectId}")
    @Results({
        @Result(column = "projectid", property = "projectId"),
        @Result(column = "projectname", property = "projectName"),
        @Result(column = "createtime", property = "createTime"),
        @Result(column = "traveldestination", property = "travelDestination"),
        @Result(column = "startdate", property = "startDate"),
        @Result(column = "projectdetails", property = "projectDetails"),
        @Result(column = "projectstatus", property = "projectStatus"),
        @Result(column = "travelnotes", property = "travelNotes")
    })
    TourProject findById(String projectId);
    
    /**
     * 查询所有旅游项目
     */
    @Select("SELECT projectid, projectname, createtime, traveldestination, startdate, projectdetails, projectstatus, travelnotes FROM travelproject")
    @Results({
        @Result(column = "projectid", property = "projectId"),
        @Result(column = "projectname", property = "projectName"),
        @Result(column = "createtime", property = "createTime"),
        @Result(column = "traveldestination", property = "travelDestination"),
        @Result(column = "startdate", property = "startDate"),
        @Result(column = "projectdetails", property = "projectDetails"),
        @Result(column = "projectstatus", property = "projectStatus"),
        @Result(column = "travelnotes", property = "travelNotes")
    })
    List<TourProject> findAll();
} 