package edu.wlxy.testspringboot1.mapper;

import edu.wlxy.testspringboot1.model.User;
import org.apache.ibatis.annotations.*;

import java.util.List;

/**
 * 用户DAO接口
 */
@Mapper
public interface UserMapper {
    
    /**
     * 新增用户
     */
    @Insert("INSERT INTO userinfo(userid, password, usertype, username, contactinfo, email, idcard, address) " +
            "VALUES(#{userId}, #{password}, #{userType}, #{username}, #{contactInfo}, #{email}, #{idCard}, #{address})")
    int insert(User user);
    
    /**
     * 删除用户
     */
    @Delete("DELETE FROM userinfo WHERE userid = #{userId}")
    int deleteById(String userId);
    
    /**
     * 更新用户
     */
    @Update("UPDATE userinfo SET password = #{password}, usertype = #{userType}, username = #{username}, " +
            "contactinfo = #{contactInfo}, email = #{email}, idcard = #{idCard}, address = #{address} " +
            "WHERE userid = #{userId}")
    int update(User user);
    
    /**
     * 根据ID查询用户
     */
    @Select("SELECT userid, password, usertype, username, contactinfo, email, idcard, address " +
            "FROM userinfo WHERE userid = #{userId}")
    @Results({
        @Result(column = "userid", property = "userId"),
        @Result(column = "password", property = "password"),
        @Result(column = "usertype", property = "userType"),
        @Result(column = "username", property = "username"),
        @Result(column = "contactinfo", property = "contactInfo"),
        @Result(column = "email", property = "email"),
        @Result(column = "idcard", property = "idCard"),
        @Result(column = "address", property = "address")
    })
    User findById(String userId);
    
    /**
     * 根据用户名查询用户
     */
    @Select("SELECT userid, password, usertype, username, contactinfo, email, idcard, address " +
            "FROM userinfo WHERE username = #{username}")
    @Results({
        @Result(column = "userid", property = "userId"),
        @Result(column = "password", property = "password"),
        @Result(column = "usertype", property = "userType"),
        @Result(column = "username", property = "username"),
        @Result(column = "contactinfo", property = "contactInfo"),
        @Result(column = "email", property = "email"),
        @Result(column = "idcard", property = "idCard"),
        @Result(column = "address", property = "address")
    })
    User findByUsername(String username);
    
    /**
     * 查询所有用户
     */
    @Select("SELECT userid, password, usertype, username, contactinfo, email, idcard, address FROM userinfo")
    @Results({
        @Result(column = "userid", property = "userId"),
        @Result(column = "password", property = "password"),
        @Result(column = "usertype", property = "userType"),
        @Result(column = "username", property = "username"),
        @Result(column = "contactinfo", property = "contactInfo"),
        @Result(column = "email", property = "email"),
        @Result(column = "idcard", property = "idCard"),
        @Result(column = "address", property = "address")
    })
    List<User> findAll();
} 