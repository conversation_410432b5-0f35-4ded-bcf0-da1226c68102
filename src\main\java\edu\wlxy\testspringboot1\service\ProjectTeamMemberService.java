package edu.wlxy.testspringboot1.service;

import edu.wlxy.testspringboot1.model.ProjectTeamMember;

import java.util.List;

/**
 * 项目团队成员服务接口
 */
public interface ProjectTeamMemberService {
    
    /**
     * 新增项目团队成员
     */
    int addProjectTeamMember(ProjectTeamMember projectTeamMember);
    
    /**
     * 删除项目团队成员
     */
    int deleteProjectTeamMember(Integer id);
    
    /**
     * 更新项目团队成员
     */
    int updateProjectTeamMember(ProjectTeamMember projectTeamMember);
    
    /**
     * 根据ID查询项目团队成员
     */
    ProjectTeamMember getProjectTeamMemberById(Integer id);
    
    /**
     * 根据项目ID查询项目团队成员
     */
    List<ProjectTeamMember> getProjectTeamMembersByProjectId(String projectId);
    
    /**
     * 查询所有项目团队成员
     */
    List<ProjectTeamMember> getAllProjectTeamMembers();
} 