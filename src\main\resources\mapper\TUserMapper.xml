<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="edu.wlxy.testspringboot1.mapper.TUserMapper">
  <resultMap id="BaseResultMap" type="edu.wlxy.testspringboot1.model.TUser">
    <id column="userid" jdbcType="VARCHAR" property="userId" />
    <result column="password" jdbcType="VARCHAR" property="password" />
    <result column="usertype" jdbcType="VARCHAR" property="userType" />
    <result column="username" jdbcType="VARCHAR" property="username" />
    <result column="contactinfo" jdbcType="VARCHAR" property="contactInfo" />
    <result column="email" jdbcType="VARCHAR" property="email" />
    <result column="idcard" jdbcType="VARCHAR" property="idCard" />
    <result column="address" jdbcType="VARCHAR" property="address" />
  </resultMap>
  
  <sql id="Base_Column_List">
    userid, password, usertype, username, contactinfo, email, idcard, address
  </sql>
  
  <!-- 根据主键查询 -->
  <select id="selectByPrimaryKey" parameterType="java.lang.String" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from userinfo
    where userid = #{userId,jdbcType=VARCHAR}
  </select>
  
  <!-- 根据主键删除 -->
  <delete id="deleteByPrimaryKey" parameterType="java.lang.String">
    delete from userinfo
    where userid = #{userId,jdbcType=VARCHAR}
  </delete>
  
  <!-- 动态插入 -->
  <insert id="insertSelective" parameterType="edu.wlxy.testspringboot1.model.TUser">
    insert into userinfo
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="userId != null">
        userid,
      </if>
      <if test="password != null">
        password,
      </if>
      <if test="userType != null">
        usertype,
      </if>
      <if test="username != null">
        username,
      </if>
      <if test="contactInfo != null">
        contactinfo,
      </if>
      <if test="email != null">
        email,
      </if>
      <if test="idCard != null">
        idcard,
      </if>
      <if test="address != null">
        address,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="userId != null">
        #{userId,jdbcType=VARCHAR},
      </if>
      <if test="password != null">
        #{password,jdbcType=VARCHAR},
      </if>
      <if test="userType != null">
        #{userType,jdbcType=VARCHAR},
      </if>
      <if test="username != null">
        #{username,jdbcType=VARCHAR},
      </if>
      <if test="contactInfo != null">
        #{contactInfo,jdbcType=VARCHAR},
      </if>
      <if test="email != null">
        #{email,jdbcType=VARCHAR},
      </if>
      <if test="idCard != null">
        #{idCard,jdbcType=VARCHAR},
      </if>
      <if test="address != null">
        #{address,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  
  <!-- 动态更新 -->
  <update id="updateByPrimaryKeySelective" parameterType="edu.wlxy.testspringboot1.model.TUser">
    update userinfo
    <set>
      <if test="password != null">
        password = #{password,jdbcType=VARCHAR},
      </if>
      <if test="userType != null">
        usertype = #{userType,jdbcType=VARCHAR},
      </if>
      <if test="username != null">
        username = #{username,jdbcType=VARCHAR},
      </if>
      <if test="contactInfo != null">
        contactinfo = #{contactInfo,jdbcType=VARCHAR},
      </if>
      <if test="email != null">
        email = #{email,jdbcType=VARCHAR},
      </if>
      <if test="idCard != null">
        idcard = #{idCard,jdbcType=VARCHAR},
      </if>
      <if test="address != null">
        address = #{address,jdbcType=VARCHAR},
      </if>
    </set>
    where userid = #{userId,jdbcType=VARCHAR}
  </update>
  
  <!-- 条件查询 -->
  <select id="selectBySelective" parameterType="edu.wlxy.testspringboot1.model.TUser" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from userinfo
    <where>
      <if test="userId != null">
        userid = #{userId,jdbcType=VARCHAR}
      </if>
      <if test="username != null">
        and username like concat('%', #{username,jdbcType=VARCHAR}, '%')
      </if>
      <if test="userType != null">
        and usertype = #{userType,jdbcType=VARCHAR}
      </if>
      <if test="email != null">
        and email = #{email,jdbcType=VARCHAR}
      </if>
      <if test="idCard != null">
        and idcard = #{idCard,jdbcType=VARCHAR}
      </if>
    </where>
  </select>
</mapper> 