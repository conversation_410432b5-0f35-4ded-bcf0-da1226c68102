package edu.wlxy.testspringboot1.mapper;

import edu.wlxy.testspringboot1.model.ProjectTeamMember;
import org.apache.ibatis.annotations.*;

import java.util.List;

/**
 * 项目团队成员DAO接口
 */
@Mapper
public interface ProjectTeamMemberMapper {
    
    /**
     * 新增项目团队成员
     */
    @Insert("INSERT INTO projectteam(projectid, memberid, memberrole, isteamleader, isprojectmanager, isregularmember, createtime) " +
            "VALUES(#{projectId}, #{memberId}, #{memberRole}, #{isTeamLeader}, #{isProjectManager}, #{isRegularMember}, #{createTime})")
    @Options(useGeneratedKeys = true, keyProperty = "id")
    int insert(ProjectTeamMember projectTeamMember);
    
    /**
     * 删除项目团队成员
     */
    @Delete("DELETE FROM projectteam WHERE id = #{id}")
    int deleteById(Integer id);
    
    /**
     * 更新项目团队成员
     */
    @Update("UPDATE projectteam SET projectid = #{projectId}, memberid = #{memberId}, memberrole = #{memberRole}, " +
            "isteamleader = #{isTeamLeader}, isprojectmanager = #{isProjectManager}, isregularmember = #{isRegularMember} " +
            "WHERE id = #{id}")
    int update(ProjectTeamMember projectTeamMember);
    
    /**
     * 根据ID查询项目团队成员
     */
    @Select("SELECT id, projectid, memberid, memberrole, isteamleader, isprojectmanager, isregularmember, createtime " +
            "FROM projectteam WHERE id = #{id}")
    @Results({
        @Result(column = "id", property = "id"),
        @Result(column = "projectid", property = "projectId"),
        @Result(column = "memberid", property = "memberId"),
        @Result(column = "memberrole", property = "memberRole"),
        @Result(column = "isteamleader", property = "isTeamLeader"),
        @Result(column = "isprojectmanager", property = "isProjectManager"),
        @Result(column = "isregularmember", property = "isRegularMember"),
        @Result(column = "createtime", property = "createTime")
    })
    ProjectTeamMember findById(Integer id);
    
    /**
     * 根据项目ID查询项目团队成员
     */
    @Select("SELECT id, projectid, memberid, memberrole, isteamleader, isprojectmanager, isregularmember, createtime " +
            "FROM projectteam WHERE projectid = #{projectId}")
    @Results({
        @Result(column = "id", property = "id"),
        @Result(column = "projectid", property = "projectId"),
        @Result(column = "memberid", property = "memberId"),
        @Result(column = "memberrole", property = "memberRole"),
        @Result(column = "isteamleader", property = "isTeamLeader"),
        @Result(column = "isprojectmanager", property = "isProjectManager"),
        @Result(column = "isregularmember", property = "isRegularMember"),
        @Result(column = "createtime", property = "createTime")
    })
    List<ProjectTeamMember> findByProjectId(String projectId);
    
    /**
     * 查询所有项目团队成员
     */
    @Select("SELECT id, projectid, memberid, memberrole, isteamleader, isprojectmanager, isregularmember, createtime FROM projectteam")
    @Results({
        @Result(column = "id", property = "id"),
        @Result(column = "projectid", property = "projectId"),
        @Result(column = "memberid", property = "memberId"),
        @Result(column = "memberrole", property = "memberRole"),
        @Result(column = "isteamleader", property = "isTeamLeader"),
        @Result(column = "isprojectmanager", property = "isProjectManager"),
        @Result(column = "isregularmember", property = "isRegularMember"),
        @Result(column = "createtime", property = "createTime")
    })
    List<ProjectTeamMember> findAll();
} 