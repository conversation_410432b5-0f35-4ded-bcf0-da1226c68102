package edu.wlxy.testspringboot1.controller;

import edu.wlxy.testspringboot1.model.ConsumptionBill;
import edu.wlxy.testspringboot1.service.ConsumptionBillService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 消费账单控制器
 */
@RestController
@RequestMapping("/api/consumptionBill")
public class ConsumptionBillController {
    
    @Autowired
    private ConsumptionBillService consumptionBillService;
    
    /**
     * 添加消费账单
     */
    @PostMapping("/add")
    public Map<String, Object> addConsumptionBill(@RequestBody ConsumptionBill consumptionBill) {
        Map<String, Object> result = new HashMap<>();
        try {
            int rows = consumptionBillService.addConsumptionBill(consumptionBill);
            if (rows > 0) {
                result.put("success", true);
                result.put("message", "添加成功");
            } else {
                result.put("success", false);
                result.put("message", "添加失败");
            }
        } catch (Exception e) {
            result.put("success", false);
            result.put("message", "添加失败：" + e.getMessage());
        }
        return result;
    }
    
    /**
     * 删除消费账单
     */
    @DeleteMapping("/delete/{id}")
    public Map<String, Object> deleteConsumptionBill(@PathVariable("id") Integer id) {
        Map<String, Object> result = new HashMap<>();
        try {
            int rows = consumptionBillService.deleteConsumptionBill(id);
            if (rows > 0) {
                result.put("success", true);
                result.put("message", "删除成功");
            } else {
                result.put("success", false);
                result.put("message", "删除失败，账单不存在");
            }
        } catch (Exception e) {
            result.put("success", false);
            result.put("message", "删除失败：" + e.getMessage());
        }
        return result;
    }
    
    /**
     * 更新消费账单
     */
    @PutMapping("/update")
    public Map<String, Object> updateConsumptionBill(@RequestBody ConsumptionBill consumptionBill) {
        Map<String, Object> result = new HashMap<>();
        try {
            int rows = consumptionBillService.updateConsumptionBill(consumptionBill);
            if (rows > 0) {
                result.put("success", true);
                result.put("message", "更新成功");
            } else {
                result.put("success", false);
                result.put("message", "更新失败，账单不存在");
            }
        } catch (Exception e) {
            result.put("success", false);
            result.put("message", "更新失败：" + e.getMessage());
        }
        return result;
    }
    
    /**
     * 根据ID查询消费账单
     */
    @GetMapping("/get/{id}")
    public Map<String, Object> getConsumptionBill(@PathVariable("id") Integer id) {
        Map<String, Object> result = new HashMap<>();
        try {
            ConsumptionBill consumptionBill = consumptionBillService.getConsumptionBillById(id);
            if (consumptionBill != null) {
                result.put("success", true);
                result.put("data", consumptionBill);
            } else {
                result.put("success", false);
                result.put("message", "账单不存在");
            }
        } catch (Exception e) {
            result.put("success", false);
            result.put("message", "查询失败：" + e.getMessage());
        }
        return result;
    }
    
    /**
     * 根据项目ID查询消费账单
     */
    @GetMapping("/getByProjectId/{projectId}")
    public Map<String, Object> getConsumptionBillsByProjectId(@PathVariable("projectId") String projectId) {
        Map<String, Object> result = new HashMap<>();
        try {
            List<ConsumptionBill> consumptionBills = consumptionBillService.getConsumptionBillsByProjectId(projectId);
            result.put("success", true);
            result.put("data", consumptionBills);
        } catch (Exception e) {
            result.put("success", false);
            result.put("message", "查询失败：" + e.getMessage());
        }
        return result;
    }
    
    /**
     * 查询所有消费账单
     */
    @GetMapping("/list")
    public Map<String, Object> getAllConsumptionBills() {
        Map<String, Object> result = new HashMap<>();
        try {
            List<ConsumptionBill> consumptionBills = consumptionBillService.getAllConsumptionBills();
            result.put("success", true);
            result.put("data", consumptionBills);
        } catch (Exception e) {
            result.put("success", false);
            result.put("message", "查询失败：" + e.getMessage());
        }
        return result;
    }
} 