package edu.wlxy.testspringboot1.mapper;

import edu.wlxy.testspringboot1.model.TUser;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * 用户DAO接口
 */
@Mapper
public interface TUserMapper {
    
    /**
     * 根据ID删除用户
     */
    int deleteByPrimaryKey(String userId);
    
    /**
     * 新增用户
     */
    int insertSelective(TUser record);
    
    /**
     * 根据ID查询用户
     */
    TUser selectByPrimaryKey(String userId);
    
    /**
     * 更新用户
     */
    int updateByPrimaryKeySelective(TUser record);
    
    /**
     * 条件查询用户
     */
    List<TUser> selectBySelective(TUser record);
} 