package edu.wlxy.testspringboot1.controller;

import edu.wlxy.testspringboot1.model.TProjectTeam;
import edu.wlxy.testspringboot1.service.TProjectTeamService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 项目团队成员控制器
 */
@RestController
@RequestMapping("/api/projectTeam")
public class TProjectTeamController {
    
    @Autowired
    private TProjectTeamService projectTeamService;
    
    /**
     * 添加项目团队成员
     */
    @PostMapping("/add")
    public Map<String, Object> addProjectTeam(@RequestBody TProjectTeam projectTeam) {
        Map<String, Object> result = new HashMap<>();
        try {
            int rows = projectTeamService.addProjectTeam(projectTeam);
            if (rows > 0) {
                result.put("success", true);
                result.put("message", "添加成功");
            } else {
                result.put("success", false);
                result.put("message", "添加失败");
            }
        } catch (Exception e) {
            result.put("success", false);
            result.put("message", "添加失败：" + e.getMessage());
        }
        return result;
    }
    
    /**
     * 删除项目团队成员
     */
    @DeleteMapping("/delete/{id}")
    public Map<String, Object> deleteProjectTeam(@PathVariable("id") Integer id) {
        Map<String, Object> result = new HashMap<>();
        try {
            int rows = projectTeamService.deleteProjectTeam(id);
            if (rows > 0) {
                result.put("success", true);
                result.put("message", "删除成功");
            } else {
                result.put("success", false);
                result.put("message", "删除失败，成员不存在");
            }
        } catch (Exception e) {
            result.put("success", false);
            result.put("message", "删除失败：" + e.getMessage());
        }
        return result;
    }
    
    /**
     * 更新项目团队成员
     */
    @PutMapping("/update")
    public Map<String, Object> updateProjectTeam(@RequestBody TProjectTeam projectTeam) {
        Map<String, Object> result = new HashMap<>();
        try {
            int rows = projectTeamService.updateProjectTeam(projectTeam);
            if (rows > 0) {
                result.put("success", true);
                result.put("message", "更新成功");
            } else {
                result.put("success", false);
                result.put("message", "更新失败，成员不存在");
            }
        } catch (Exception e) {
            result.put("success", false);
            result.put("message", "更新失败：" + e.getMessage());
        }
        return result;
    }
    
    /**
     * 根据ID查询项目团队成员
     */
    @GetMapping("/get/{id}")
    public Map<String, Object> getProjectTeam(@PathVariable("id") Integer id) {
        Map<String, Object> result = new HashMap<>();
        try {
            TProjectTeam projectTeam = projectTeamService.getProjectTeamById(id);
            if (projectTeam != null) {
                result.put("success", true);
                result.put("data", projectTeam);
            } else {
                result.put("success", false);
                result.put("message", "成员不存在");
            }
        } catch (Exception e) {
            result.put("success", false);
            result.put("message", "查询失败：" + e.getMessage());
        }
        return result;
    }
    
    /**
     * 条件查询项目团队成员
     */
    @PostMapping("/list")
    public Map<String, Object> getProjectTeamsByCondition(@RequestBody(required = false) TProjectTeam projectTeam) {
        Map<String, Object> result = new HashMap<>();
        try {
            if (projectTeam == null) {
                projectTeam = new TProjectTeam();
            }
            List<TProjectTeam> projectTeams = projectTeamService.getProjectTeamsByCondition(projectTeam);
            result.put("success", true);
            result.put("data", projectTeams);
        } catch (Exception e) {
            result.put("success", false);
            result.put("message", "查询失败：" + e.getMessage());
        }
        return result;
    }
} 