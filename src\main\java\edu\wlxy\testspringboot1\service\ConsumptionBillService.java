package edu.wlxy.testspringboot1.service;

import edu.wlxy.testspringboot1.model.ConsumptionBill;

import java.util.List;

/**
 * 消费账单服务接口
 */
public interface ConsumptionBillService {
    
    /**
     * 新增消费账单
     */
    int addConsumptionBill(ConsumptionBill consumptionBill);
    
    /**
     * 删除消费账单
     */
    int deleteConsumptionBill(Integer id);
    
    /**
     * 更新消费账单
     */
    int updateConsumptionBill(ConsumptionBill consumptionBill);
    
    /**
     * 根据ID查询消费账单
     */
    ConsumptionBill getConsumptionBillById(Integer id);
    
    /**
     * 根据项目ID查询消费账单
     */
    List<ConsumptionBill> getConsumptionBillsByProjectId(String projectId);
    
    /**
     * 查询所有消费账单
     */
    List<ConsumptionBill> getAllConsumptionBills();
} 