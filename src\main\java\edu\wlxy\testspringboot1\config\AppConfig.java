package edu.wlxy.testspringboot1.config;

import org.springframework.beans.factory.annotation.Configurable;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.servlet.config.annotation.ResourceHandlerRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;

@Configuration
public class AppConfig implements WebMvcConfigurer {
    @Value("${fileData.realUploadPath}") //从配置文件中取出代表实际路径的字符串,注入给下面的变量
    private String realUploadPath; //真实路径
    @Value("${fileData.virtualAccessPath}")//从配置文件中取出代表虚拟路径的字符串,注入给下面的变量
    private String virtualAccessPath;//虚拟路径

    @Override
    public void addResourceHandlers(ResourceHandlerRegistry registry){
        registry.addResourceHandler(virtualAccessPath).addResourceLocations("file:"+realUploadPath);
    }
}
