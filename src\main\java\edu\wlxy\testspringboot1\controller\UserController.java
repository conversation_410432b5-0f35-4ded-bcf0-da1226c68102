package edu.wlxy.testspringboot1.controller;

import edu.wlxy.testspringboot1.model.TUser;
import edu.wlxy.testspringboot1.service.TUserService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.servlet.mvc.support.RedirectAttributes;

import javax.servlet.http.HttpSession;
import java.util.List;

/**
 * 用户控制器
 */
@Controller
@RequestMapping("/user")
public class UserController {
    
    @Autowired
    private TUserService userService;
    
    /**
     * 跳转到登录页面
     */
    @GetMapping("/toLogin")
    public String toLogin() {
        return "login";
    }
    
    /**
     * 跳转到注册页面
     */
    @GetMapping("/toRegister")
    public String toRegister() {
        return "register";
    }
    
    /**
     * 用户登录
     */
    @PostMapping("/login")
    public String login(String username, String password, HttpSession session, RedirectAttributes redirectAttributes) {
        // 创建查询条件
        TUser queryUser = new TUser();
        queryUser.setUserId(username); // 使用用户输入的用户名作为userId查询
        
        // 查询用户
        List<TUser> users = userService.getUsersByCondition(queryUser);
        
        if (users != null && !users.isEmpty()) {
            TUser user = users.get(0);
            // 验证密码
            if (password.equals(user.getPassword())) {
                // 登录成功，将用户信息存入session
                session.setAttribute("user", user);
                return "redirect:/project/index";
            }
        }
        
        // 登录失败
        redirectAttributes.addFlashAttribute("errorMsg", "用户名或密码错误");
        return "redirect:/user/toLogin";
    }
    
    /**
     * 用户注册
     */
    @PostMapping("/register")
    public String register(TUser user, String confirmPassword, RedirectAttributes redirectAttributes) {
        // 检查用户ID是否已存在
        TUser existingUser = userService.getUserById(user.getUserId());
        if (existingUser != null) {
            redirectAttributes.addFlashAttribute("errorMsg", "用户ID已存在");
            return "redirect:/user/toRegister";
        }
        
        // 检查两次密码是否一致
        if (!user.getPassword().equals(confirmPassword)) {
            redirectAttributes.addFlashAttribute("errorMsg", "两次输入的密码不一致");
            return "redirect:/user/toRegister";
        }
        
        // 设置默认用户类型
        if (user.getUserType() == null || user.getUserType().isEmpty()) {
            user.setUserType("普通用户");
        }
        
        // 保存用户
        int result = userService.addUser(user);
        
        if (result > 0) {
            redirectAttributes.addFlashAttribute("successMsg", "注册成功，请登录");
            return "redirect:/user/toLogin";
        } else {
            redirectAttributes.addFlashAttribute("errorMsg", "注册失败，请稍后重试");
            return "redirect:/user/toRegister";
        }
    }
    
    /**
     * 用户退出
     */
    @GetMapping("/logout")
    public String logout(HttpSession session) {
        // 清除session
        session.invalidate();
        return "redirect:/user/toLogin";
    }
} 