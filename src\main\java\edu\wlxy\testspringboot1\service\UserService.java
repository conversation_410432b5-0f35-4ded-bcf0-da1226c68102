package edu.wlxy.testspringboot1.service;

import edu.wlxy.testspringboot1.model.User;

import java.util.List;

/**
 * 用户服务接口
 */
public interface UserService {
    
    /**
     * 新增用户
     */
    int addUser(User user);
    
    /**
     * 删除用户
     */
    int deleteUser(String userId);
    
    /**
     * 更新用户
     */
    int updateUser(User user);
    
    /**
     * 根据ID查询用户
     */
    User getUserById(String userId);
    
    /**
     * 根据用户名查询用户
     */
    User getUserByUsername(String username);
    
    /**
     * 查询所有用户
     */
    List<User> getAllUsers();
} 