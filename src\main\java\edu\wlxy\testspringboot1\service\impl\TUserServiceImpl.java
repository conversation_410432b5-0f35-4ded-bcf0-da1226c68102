package edu.wlxy.testspringboot1.service.impl;

import edu.wlxy.testspringboot1.mapper.TUserMapper;
import edu.wlxy.testspringboot1.model.TUser;
import edu.wlxy.testspringboot1.service.TUserService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 用户服务实现类
 */
@Service
public class TUserServiceImpl implements TUserService {
    
    @Autowired
    private TUserMapper userMapper;
    
    @Override
    public int addUser(TUser user) {
        return userMapper.insertSelective(user);
    }
    
    @Override
    public int deleteUser(String userId) {
        return userMapper.deleteByPrimaryKey(userId);
    }
    
    @Override
    public int updateUser(TUser user) {
        return userMapper.updateByPrimaryKeySelective(user);
    }
    
    @Override
    public TUser getUserById(String userId) {
        return userMapper.selectByPrimary<PERSON>ey(userId);
    }
    
    @Override
    public List<TUser> getUsersByCondition(TUser user) {
        return userMapper.selectBySelective(user);
    }
} 