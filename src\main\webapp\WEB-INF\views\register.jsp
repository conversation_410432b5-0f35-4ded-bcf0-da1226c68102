<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>注册 - 旅游项目管理系统</title>
  <link rel="stylesheet" href="${pageContext.request.contextPath}/static/css/register.css">
</head>
<body>
<div class="register-container">
  <h2>管理员注册</h2>
  <form action="${pageContext.request.contextPath}/user/register" method="post">
    <div class="form-group">
      <label for="userId">用户ID：</label>
      <input type="text" id="userId" name="userId" required>
    </div>
    <div class="form-group">
      <label for="username">用户名：</label>
      <input type="text" id="username" name="username" required>
    </div>
    <div class="form-group">
      <label for="password">密码：</label>
      <input type="password" id="password" name="password" required>
    </div>
    <div class="form-group">
      <label for="confirmPassword">确认密码：</label>
      <input type="password" id="confirmPassword" name="confirmPassword" required>
    </div>
    <div class="form-group">
      <label for="email">电子邮箱：</label>
      <input type="email" id="email" name="email">
    </div>
    <div class="form-group">
      <label for="contactInfo">联系方式：</label>
      <input type="text" id="contactInfo" name="contactInfo">
    </div>
    <div class="form-buttons">
      <button type="submit">注册</button>
      <a href="${pageContext.request.contextPath}/user/toLogin">返回登录</a>
    </div>
    <c:if test="${not empty errorMsg}">
      <div class="error-message">
        ${errorMsg}
      </div>
    </c:if>
  </form>
</div>
</body>
</html> 