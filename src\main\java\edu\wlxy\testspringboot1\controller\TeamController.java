package edu.wlxy.testspringboot1.controller;

import edu.wlxy.testspringboot1.model.TProjectTeam;
import edu.wlxy.testspringboot1.model.TTravelProject;
import edu.wlxy.testspringboot1.model.TUser;
import edu.wlxy.testspringboot1.service.TProjectTeamService;
import edu.wlxy.testspringboot1.service.TTravelProjectService;
import edu.wlxy.testspringboot1.service.TUserService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.ui.Model;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.mvc.support.RedirectAttributes;

import javax.servlet.http.HttpSession;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.HashMap;

/**
 * 团队成员控制器
 */
@Controller
@RequestMapping("/team")
public class TeamController {
    
    @Autowired
    private TProjectTeamService projectTeamService;
    
    @Autowired
    private TTravelProjectService travelProjectService;
    
    @Autowired
    private TUserService userService;
    
    /**
     * 跳转到添加团队成员页面
     */
    @GetMapping("/toAdd")
    public String toAdd(Model model, HttpSession session) {
        // 检查用户是否登录
        if (session.getAttribute("user") == null) {
            return "redirect:/user/toLogin";
        }
        
        // 获取所有项目
        TTravelProject query = new TTravelProject();
        List<TTravelProject> projects = travelProjectService.getTravelProjectsByCondition(query);
        model.addAttribute("projects", projects);
        
        // 获取所有用户
        TUser userQuery = new TUser();
        List<TUser> users = userService.getUsersByCondition(userQuery);
        model.addAttribute("users", users);
        
        // 获取所有团队成员（带项目名和用户名）
        List<Map<String, Object>> teamMembers = getTeamMembersWithDetails();
        model.addAttribute("teamMembers", teamMembers);
        
        return "add-team";
    }
    
    /**
     * 添加团队成员
     */
    @PostMapping("/add")
    public String add(TProjectTeam projectTeam, RedirectAttributes redirectAttributes) {
        // 设置创建时间
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        projectTeam.setCreateTime(sdf.format(new Date()));
        
        // 处理复选框值
        if (projectTeam.getIsTeamLeader() == null) {
            projectTeam.setIsTeamLeader(false);
        }
        if (projectTeam.getIsProjectManager() == null) {
            projectTeam.setIsProjectManager(false);
        }
        if (projectTeam.getIsRegularMember() == null) {
            projectTeam.setIsRegularMember(false);
        }
        
        int result = projectTeamService.addProjectTeam(projectTeam);
        
        if (result > 0) {
            redirectAttributes.addFlashAttribute("successMsg", "团队成员添加成功");
        } else {
            redirectAttributes.addFlashAttribute("errorMsg", "团队成员添加失败");
        }
        
        return "redirect:/team/toAdd";
    }
    
    /**
     * 删除团队成员
     */
    @GetMapping("/delete/{id}")
    public String delete(@PathVariable("id") Integer id, RedirectAttributes redirectAttributes) {
        int result = projectTeamService.deleteProjectTeam(id);
        
        if (result > 0) {
            redirectAttributes.addFlashAttribute("successMsg", "团队成员删除成功");
        } else {
            redirectAttributes.addFlashAttribute("errorMsg", "团队成员删除失败");
        }
        
        return "redirect:/team/toAdd";
    }
    
    /**
     * 跳转到编辑团队成员页面
     */
    @GetMapping("/edit/{id}")
    public String toEdit(@PathVariable("id") Integer id, Model model, HttpSession session) {
        // 检查用户是否登录
        if (session.getAttribute("user") == null) {
            return "redirect:/user/toLogin";
        }
        
        // 获取团队成员信息
        TProjectTeam projectTeam = projectTeamService.getProjectTeamById(id);
        if (projectTeam == null) {
            return "redirect:/team/toAdd";
        }
        model.addAttribute("projectTeam", projectTeam);
        
        // 获取所有项目
        TTravelProject query = new TTravelProject();
        List<TTravelProject> projects = travelProjectService.getTravelProjectsByCondition(query);
        model.addAttribute("projects", projects);
        
        // 获取所有用户
        TUser userQuery = new TUser();
        List<TUser> users = userService.getUsersByCondition(userQuery);
        model.addAttribute("users", users);
        
        return "edit-team";
    }
    
    /**
     * 更新团队成员
     */
    @PostMapping("/update")
    public String update(TProjectTeam projectTeam, RedirectAttributes redirectAttributes) {
        // 处理复选框值
        if (projectTeam.getIsTeamLeader() == null) {
            projectTeam.setIsTeamLeader(false);
        }
        if (projectTeam.getIsProjectManager() == null) {
            projectTeam.setIsProjectManager(false);
        }
        if (projectTeam.getIsRegularMember() == null) {
            projectTeam.setIsRegularMember(false);
        }
        
        int result = projectTeamService.updateProjectTeam(projectTeam);
        
        if (result > 0) {
            redirectAttributes.addFlashAttribute("successMsg", "团队成员更新成功");
        } else {
            redirectAttributes.addFlashAttribute("errorMsg", "团队成员更新失败");
        }
        
        return "redirect:/team/toAdd";
    }
    
    /**
     * 团队成员列表
     */
    @GetMapping("/list")
    public String list(Model model, HttpSession session) {
        // 检查用户是否登录
        if (session.getAttribute("user") == null) {
            return "redirect:/user/toLogin";
        }
        
        // 获取所有项目
        TTravelProject query = new TTravelProject();
        List<TTravelProject> projects = travelProjectService.getTravelProjectsByCondition(query);
        model.addAttribute("projects", projects);
        
        // 获取所有团队成员（带项目名和用户名）
        List<Map<String, Object>> teamMembers = getTeamMembersWithDetails();
        model.addAttribute("teamMembers", teamMembers);
        
        return "team-list";
    }
    
    /**
     * 获取团队成员详细信息（包括项目名和用户名）
     */
    private List<Map<String, Object>> getTeamMembersWithDetails() {
        // 获取所有团队成员
        TProjectTeam query = new TProjectTeam();
        List<TProjectTeam> teamMembers = projectTeamService.getProjectTeamsByCondition(query);
        
        List<Map<String, Object>> result = new ArrayList<>();
        
        for (TProjectTeam member : teamMembers) {
            Map<String, Object> memberMap = new HashMap<>();
            memberMap.put("id", member.getId());
            memberMap.put("projectId", member.getProjectId());
            memberMap.put("memberId", member.getMemberId());
            memberMap.put("memberRole", member.getMemberRole());
            memberMap.put("isTeamLeader", member.getIsTeamLeader());
            memberMap.put("isProjectManager", member.getIsProjectManager());
            memberMap.put("isRegularMember", member.getIsRegularMember());
            memberMap.put("createTime", member.getCreateTime());
            
            // 获取项目名称
            TTravelProject project = travelProjectService.getTravelProjectById(member.getProjectId());
            if (project != null) {
                memberMap.put("projectName", project.getProjectName());
            } else {
                memberMap.put("projectName", "未知项目");
            }
            
            // 获取用户名称
            TUser user = userService.getUserById(member.getMemberId());
            if (user != null) {
                memberMap.put("memberName", user.getUsername());
            } else {
                memberMap.put("memberName", "未知用户");
            }
            
            result.add(memberMap);
        }
        
        return result;
    }
} 