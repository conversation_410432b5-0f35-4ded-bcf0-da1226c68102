package edu.wlxy.testspringboot1.controller;

import edu.wlxy.testspringboot1.model.ProjectTeamMember;
import edu.wlxy.testspringboot1.service.ProjectTeamMemberService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 项目团队成员控制器
 */
@RestController
@RequestMapping("/api/projectTeamMember")
public class ProjectTeamMemberController {
    
    @Autowired
    private ProjectTeamMemberService projectTeamMemberService;
    
    /**
     * 添加项目团队成员
     */
    @PostMapping("/add")
    public Map<String, Object> addProjectTeamMember(@RequestBody ProjectTeamMember projectTeamMember) {
        Map<String, Object> result = new HashMap<>();
        try {
            int rows = projectTeamMemberService.addProjectTeamMember(projectTeamMember);
            if (rows > 0) {
                result.put("success", true);
                result.put("message", "添加成功");
            } else {
                result.put("success", false);
                result.put("message", "添加失败");
            }
        } catch (Exception e) {
            result.put("success", false);
            result.put("message", "添加失败：" + e.getMessage());
        }
        return result;
    }
    
    /**
     * 删除项目团队成员
     */
    @DeleteMapping("/delete/{id}")
    public Map<String, Object> deleteProjectTeamMember(@PathVariable("id") Integer id) {
        Map<String, Object> result = new HashMap<>();
        try {
            int rows = projectTeamMemberService.deleteProjectTeamMember(id);
            if (rows > 0) {
                result.put("success", true);
                result.put("message", "删除成功");
            } else {
                result.put("success", false);
                result.put("message", "删除失败，成员不存在");
            }
        } catch (Exception e) {
            result.put("success", false);
            result.put("message", "删除失败：" + e.getMessage());
        }
        return result;
    }
    
    /**
     * 更新项目团队成员
     */
    @PutMapping("/update")
    public Map<String, Object> updateProjectTeamMember(@RequestBody ProjectTeamMember projectTeamMember) {
        Map<String, Object> result = new HashMap<>();
        try {
            int rows = projectTeamMemberService.updateProjectTeamMember(projectTeamMember);
            if (rows > 0) {
                result.put("success", true);
                result.put("message", "更新成功");
            } else {
                result.put("success", false);
                result.put("message", "更新失败，成员不存在");
            }
        } catch (Exception e) {
            result.put("success", false);
            result.put("message", "更新失败：" + e.getMessage());
        }
        return result;
    }
    
    /**
     * 根据ID查询项目团队成员
     */
    @GetMapping("/get/{id}")
    public Map<String, Object> getProjectTeamMember(@PathVariable("id") Integer id) {
        Map<String, Object> result = new HashMap<>();
        try {
            ProjectTeamMember projectTeamMember = projectTeamMemberService.getProjectTeamMemberById(id);
            if (projectTeamMember != null) {
                result.put("success", true);
                result.put("data", projectTeamMember);
            } else {
                result.put("success", false);
                result.put("message", "成员不存在");
            }
        } catch (Exception e) {
            result.put("success", false);
            result.put("message", "查询失败：" + e.getMessage());
        }
        return result;
    }
    
    /**
     * 根据项目ID查询项目团队成员
     */
    @GetMapping("/getByProjectId/{projectId}")
    public Map<String, Object> getProjectTeamMembersByProjectId(@PathVariable("projectId") String projectId) {
        Map<String, Object> result = new HashMap<>();
        try {
            List<ProjectTeamMember> projectTeamMembers = projectTeamMemberService.getProjectTeamMembersByProjectId(projectId);
            result.put("success", true);
            result.put("data", projectTeamMembers);
        } catch (Exception e) {
            result.put("success", false);
            result.put("message", "查询失败：" + e.getMessage());
        }
        return result;
    }
    
    /**
     * 查询所有项目团队成员
     */
    @GetMapping("/list")
    public Map<String, Object> getAllProjectTeamMembers() {
        Map<String, Object> result = new HashMap<>();
        try {
            List<ProjectTeamMember> projectTeamMembers = projectTeamMemberService.getAllProjectTeamMembers();
            result.put("success", true);
            result.put("data", projectTeamMembers);
        } catch (Exception e) {
            result.put("success", false);
            result.put("message", "查询失败：" + e.getMessage());
        }
        return result;
    }
} 