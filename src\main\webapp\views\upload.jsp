<%--
  上传文件案例网页
--%>
<%@ page contentType="text/html;charset=UTF-8" language="java" %>
<html>
<head>
    <title>Title</title>
</head>
<body>

    <input type="file" id="fileToUpload">
    <input type="button" id="choicePhoto" value="上传文件">
    上传到服务器上的文件新名称：<input type="text" style="width:350px" id="photourl">
    <br><br>加载上传到服务器上的图片显示<br>
    <img id="showPhoto" style="width:350px; height:250px; border: darkgray" src=""/>

        <script type="text/javascript" src="../js/jquery-3.5.1.js"></script>
    <script>
        $(function (){
            $("#choicePhoto").click(function (){
                var files = $("#fileToUpload")[0].files;
                if(files.length<=0){
                    return alert('请选择文件后再上传');
                }

                var formData = new FormData();
                formData.append("file",files[0]);
                //alert("请求")
                alert("上传"+files[0].name+"到服务器上")
                $.ajax({
                    url:"../uploadfile",
                    data:formData,
                    cache:false,
                    type:"post",
                    datatype:"json",
                    contentType:false, //不设置内容类型
                    processData:false, //不处理数据
                    success:function (data){
                        if(data.code=1) {
                            alert(data.msg);
                            $("#photourl").val(data.data);
                            $("#showPhoto").attr("src","../upload/"+data.data)
                        }else{
                            alert(data.msg);
                        }
                    }
                })
            })
        })
    </script>
</body>
</html>
