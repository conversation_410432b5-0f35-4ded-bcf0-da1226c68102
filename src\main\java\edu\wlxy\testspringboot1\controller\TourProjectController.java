package edu.wlxy.testspringboot1.controller;

import edu.wlxy.testspringboot1.model.TourProject;
import edu.wlxy.testspringboot1.service.TourProjectService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 旅游项目控制器
 */
@RestController
@RequestMapping("/api/tourProject")
public class TourProjectController {
    
    @Autowired
    private TourProjectService tourProjectService;
    
    /**
     * 添加旅游项目
     */
    @PostMapping("/add")
    public Map<String, Object> addTourProject(@RequestBody TourProject tourProject) {
        Map<String, Object> result = new HashMap<>();
        try {
            int rows = tourProjectService.addTourProject(tourProject);
            if (rows > 0) {
                result.put("success", true);
                result.put("message", "添加成功");
            } else {
                result.put("success", false);
                result.put("message", "添加失败");
            }
        } catch (Exception e) {
            result.put("success", false);
            result.put("message", "添加失败：" + e.getMessage());
        }
        return result;
    }
    
    /**
     * 删除旅游项目
     */
    @DeleteMapping("/delete/{projectId}")
    public Map<String, Object> deleteTourProject(@PathVariable("projectId") String projectId) {
        Map<String, Object> result = new HashMap<>();
        try {
            int rows = tourProjectService.deleteTourProject(projectId);
            if (rows > 0) {
                result.put("success", true);
                result.put("message", "删除成功");
            } else {
                result.put("success", false);
                result.put("message", "删除失败，项目不存在");
            }
        } catch (Exception e) {
            result.put("success", false);
            result.put("message", "删除失败：" + e.getMessage());
        }
        return result;
    }
    
    /**
     * 更新旅游项目
     */
    @PutMapping("/update")
    public Map<String, Object> updateTourProject(@RequestBody TourProject tourProject) {
        Map<String, Object> result = new HashMap<>();
        try {
            int rows = tourProjectService.updateTourProject(tourProject);
            if (rows > 0) {
                result.put("success", true);
                result.put("message", "更新成功");
            } else {
                result.put("success", false);
                result.put("message", "更新失败，项目不存在");
            }
        } catch (Exception e) {
            result.put("success", false);
            result.put("message", "更新失败：" + e.getMessage());
        }
        return result;
    }
    
    /**
     * 根据ID查询旅游项目
     */
    @GetMapping("/get/{projectId}")
    public Map<String, Object> getTourProject(@PathVariable("projectId") String projectId) {
        Map<String, Object> result = new HashMap<>();
        try {
            TourProject tourProject = tourProjectService.getTourProjectById(projectId);
            if (tourProject != null) {
                result.put("success", true);
                result.put("data", tourProject);
            } else {
                result.put("success", false);
                result.put("message", "项目不存在");
            }
        } catch (Exception e) {
            result.put("success", false);
            result.put("message", "查询失败：" + e.getMessage());
        }
        return result;
    }
    
    /**
     * 查询所有旅游项目
     */
    @GetMapping("/list")
    public Map<String, Object> getAllTourProjects() {
        Map<String, Object> result = new HashMap<>();
        try {
            List<TourProject> tourProjects = tourProjectService.getAllTourProjects();
            result.put("success", true);
            result.put("data", tourProjects);
        } catch (Exception e) {
            result.put("success", false);
            result.put("message", "查询失败：" + e.getMessage());
        }
        return result;
    }
} 