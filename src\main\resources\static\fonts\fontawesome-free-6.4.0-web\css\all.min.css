/* 
 * 這是一個簡化版的 Font Awesome CSS 文件
 * 實際使用時應下載完整的 Font Awesome 庫
 */

.fa,
.fas,
.far,
.fal,
.fad,
.fab {
  -moz-osx-font-smoothing: grayscale;
  -webkit-font-smoothing: antialiased;
  display: inline-block;
  font-style: normal;
  font-variant: normal;
  text-rendering: auto;
  line-height: 1;
}

/* Font Awesome 核心樣式 */
.fa {
  font-family: 'Font Awesome 5 Free';
  font-weight: 400;
}

.fas {
  font-family: 'Font Awesome 5 Free';
  font-weight: 900;
}

.far {
  font-family: 'Font Awesome 5 Free';
  font-weight: 400;
}

.fab {
  font-family: 'Font Awesome 5 Brands';
  font-weight: 400;
}

/* 基本圖標樣式 */
.fa-edit:before {
  content: "\f044";
}

.fa-check-circle:before {
  content: "\f058";
}

.fa-spinner:before {
  content: "\f110";
}

.fa-clock:before {
  content: "\f017";
}

.fa-search:before {
  content: "\f002";
}

.fa-plus:before {
  content: "\f067";
}

.fa-hashtag:before {
  content: "\f292";
}

.fa-tag:before {
  content: "\f02b";
}

.fa-calendar:before {
  content: "\f133";
}

.fa-map-marker:before {
  content: "\f041";
}

.fa-refresh:before {
  content: "\f021";
}

.fa-list:before {
  content: "\f03a";
}

.fa-download:before {
  content: "\f019";
}

.fa-cog:before {
  content: "\f013";
}

.fa-bookmark:before {
  content: "\f02e";
}

.fa-eye:before {
  content: "\f06e";
}

.fa-pencil:before {
  content: "\f040";
}

.fa-trash:before {
  content: "\f1f8";
}

.fa-angle-double-left:before {
  content: "\f100";
}

.fa-angle-left:before {
  content: "\f104";
}

.fa-angle-right:before {
  content: "\f105";
}

.fa-angle-double-right:before {
  content: "\f101";
}

.fa-file-invoice:before {
  content: "\f570";
}

.fa-project-diagram:before {
  content: "\f542";
}

.fa-chart-pie:before {
  content: "\f200";
}

.fa-receipt:before {
  content: "\f543";
}

.fa-money-bill-wave:before {
  content: "\f53a";
}

.fa-calculator:before {
  content: "\f1ec";
}

.fa-print:before {
  content: "\f02f";
} 