# 应用服务 WEB 访问端口
server.port=8080
#为项目定义一个URL访问地址
server.servlet.context-path=/testspringboot

#配置图片实际路径
fileData.realUploadPath=C:/Users/<USER>/Pictures/Saved Pictures/
#访问图片的虚拟URL地址
fileData.virtualAccessPath=/upload/**

# 设置单个文件上传的最大大小
spring.servlet.multipart.max-file-size=100MB
# 设置一次请求中所有文件的总大小
spring.servlet.multipart.max-request-size=100MB

# 数据库配置
spring.datasource.driver-class-name=com.mysql.cj.jdbc.Driver
spring.datasource.url=*****************************************************************************************************************
spring.datasource.username=root
spring.datasource.password=root

# MyBatis 配置
mybatis.mapper-locations=classpath:mapper/*.xml
mybatis.type-aliases-package=edu.wlxy.testspringboot1.model
mybatis.configuration.map-underscore-to-camel-case=true

# JSP 视图解析器配置
spring.mvc.view.prefix=/WEB-INF/views/
spring.mvc.view.suffix=.jsp

# 静态资源配置
spring.web.resources.static-locations=classpath:/static/,classpath:/public/,classpath:/resources/,classpath:/META-INF/resources/
spring.mvc.static-path-pattern=/static/**