<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>
<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>添加消费账单 - 旅游项目管理系统</title>
  <link rel="stylesheet" href="${pageContext.request.contextPath}/static/css/style.css">
  <link rel="stylesheet" href="${pageContext.request.contextPath}/static/fonts/fontawesome-free-6.4.0-web/css/all.min.css">
  <link rel="stylesheet" href="${pageContext.request.contextPath}/static/css/add-expense.css">
</head>
<body>
<jsp:include page="common/nav.jsp" />

<div class="main-content">
  <div class="container">
    <div class="page-header">
      <div class="header-wrapper">
        <h2><i class="fa fa-money-bill"></i> 添加消费账单</h2>
        <p class="subtitle">记录项目消费明细</p>
      </div>
    </div>

    <div class="form-container">
      <form action="${pageContext.request.contextPath}/expense/add" method="post">
        <div class="form-section">
          <h3><i class="fa fa-project-diagram"></i> 项目信息</h3>
          <div class="form-row">
            <div class="form-group">
              <label for="projectId">选择项目：</label>
              <select id="projectId" name="projectId" required>
                <option value="">--请选择项目--</option>
                <c:forEach items="${projects}" var="project">
                  <option value="${project.projectId}">${project.projectName}</option>
                </c:forEach>
              </select>
            </div>
          </div>
        </div>

        <div class="form-section">
          <h3><i class="fa fa-receipt"></i> 消费信息</h3>
          <div class="form-row">
            <div class="form-group">
              <label for="expenseType">消费类型：</label>
              <select id="expenseType" name="expenseType" required>
                <option value="">--请选择消费类型--</option>
                <option value="住宿">住宿</option>
                <option value="餐饮">餐饮</option>
                <option value="交通">交通</option>
                <option value="门票">门票</option>
                <option value="购物">购物</option>
                <option value="其他">其他</option>
              </select>
            </div>
            <div class="form-group">
              <label for="expenseDate">消费日期：</label>
              <input type="date" id="expenseDate" name="expenseDate" required>
            </div>
          </div>
          <div class="form-row">
            <div class="form-group">
              <label for="price">单价：</label>
              <input type="number" id="price" name="price" step="0.01" min="0" required>
            </div>
            <div class="form-group">
              <label for="quantity">数量：</label>
              <input type="number" id="quantity" name="quantity" min="1" value="1" required>
            </div>
            <div class="form-group">
              <label for="expenseAmount">总金额：</label>
              <input type="number" id="expenseAmount" name="expenseAmount" step="0.01" min="0" readonly>
            </div>
          </div>
          <div class="form-row">
            <div class="form-group full-width">
              <label for="expenseNotes">备注：</label>
              <textarea id="expenseNotes" name="expenseNotes" rows="4"></textarea>
            </div>
          </div>
        </div>

        <div class="form-actions">
          <button type="submit" class="btn-primary">
            <i class="fa fa-save"></i> 保存账单
          </button>
          <a href="${pageContext.request.contextPath}/expense/list" class="btn-secondary">
            <i class="fa fa-times"></i> 取消
          </a>
        </div>
      </form>
    </div>

    <!-- 最近消费记录 -->
    <div class="expense-list">
      <div class="list-header">
        <h3><i class="fa fa-list"></i> 最近消费记录</h3>
        <div class="filter-controls">
          <select id="projectFilter" onchange="filterExpenses()">
            <option value="">所有项目</option>
            <c:forEach items="${projects}" var="project">
              <option value="${project.projectId}">${project.projectName}</option>
            </c:forEach>
          </select>
        </div>
      </div>
      <div class="table-responsive">
        <table>
          <thead>
          <tr>
            <th>ID</th>
            <th>项目</th>
            <th>消费类型</th>
            <th>单价</th>
            <th>数量</th>
            <th>总金额</th>
            <th>消费日期</th>
            <th>操作</th>
          </tr>
          </thead>
          <tbody id="expensesTable">
          <c:forEach items="${recentExpenses}" var="expense">
            <tr data-project-id="${expense.projectId}">
              <td>${expense.id}</td>
              <td>${expense.projectName}</td>
              <td>${expense.expenseType}</td>
              <td>¥${expense.price}</td>
              <td>${expense.quantity}</td>
              <td>¥${expense.expenseAmount}</td>
              <td>${expense.expenseDate}</td>
              <td class="action-buttons">
                <a href="${pageContext.request.contextPath}/expense/edit/${expense.id}" class="btn-action" title="编辑">
                  <i class="fa fa-pencil"></i>
                </a>
                <a href="javascript:void(0)" onclick="deleteExpense(${expense.id})" class="btn-action" title="删除">
                  <i class="fa fa-trash"></i>
                </a>
              </td>
            </tr>
          </c:forEach>
          </tbody>
        </table>
      </div>
    </div>
  </div>
</div>

<script>
  // 自动计算总金额
  document.addEventListener('DOMContentLoaded', function() {
    const priceInput = document.getElementById('price');
    const quantityInput = document.getElementById('quantity');
    const totalInput = document.getElementById('expenseAmount');
    
    function calculateTotal() {
      const price = parseFloat(priceInput.value) || 0;
      const quantity = parseInt(quantityInput.value) || 0;
      totalInput.value = (price * quantity).toFixed(2);
    }
    
    priceInput.addEventListener('input', calculateTotal);
    quantityInput.addEventListener('input', calculateTotal);
    
    // 初始计算
    calculateTotal();
  });
  
  function filterExpenses() {
    const projectId = document.getElementById('projectFilter').value;
    const rows = document.querySelectorAll('#expensesTable tr');
    
    rows.forEach(row => {
      if (!projectId || row.getAttribute('data-project-id') === projectId) {
        row.style.display = '';
      } else {
        row.style.display = 'none';
      }
    });
  }
  
  function deleteExpense(id) {
    if (confirm('确定要删除此消费记录吗？')) {
      location.href = '${pageContext.request.contextPath}/expense/delete/' + id;
    }
  }
</script>
</body>
</html> 