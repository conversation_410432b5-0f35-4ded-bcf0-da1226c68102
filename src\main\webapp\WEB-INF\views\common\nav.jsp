<%@ page language="java" contentType="text/html; charset=UTF-8" pageEncoding="UTF-8"%>
<%@ taglib prefix="c" uri="http://java.sun.com/jsp/jstl/core" %>

<nav class="sidebar">
  <div class="logo">
    <img src="${pageContext.request.contextPath}/static/images/2-12.png" alt="Logo">
    <span class="company-name">旅游共享记账系统</span>
  </div>
  <ul class="nav-links">
    <li><a href="${pageContext.request.contextPath}/project/index"><i class="fas fa-home"></i>首页</a></li>
    <li><a href="${pageContext.request.contextPath}/team/toAdd"><i class="fas fa-users"></i>添加团队</a></li>
    <li><a href="${pageContext.request.contextPath}/expense/toAdd"><i class="fas fa-money-bill"></i>添加消费</a></li>
    <li><a href="${pageContext.request.contextPath}/project/toEdit"><i class="fas fa-edit"></i>编辑项目</a></li>
    <li><a href="${pageContext.request.contextPath}/expense/view"><i class="fas fa-file-invoice"></i>查看账单</a></li>
    <li class="dropdown">
      <a href="#"><i class="fas fa-clipboard-list"></i>订单管理<i class="fas fa-chevron-down arrow"></i></a>
      <ul class="dropdown-content">
        <li><a href="${pageContext.request.contextPath}/order/pending"><i class="fas fa-clock"></i>待处理订单</a></li>
        <li><a href="${pageContext.request.contextPath}/order/list"><i class="fas fa-list"></i>订单列表</a></li>
        <li><a href="${pageContext.request.contextPath}/order/validate"><i class="fas fa-check-circle"></i>订单验证</a></li>
        <li><a href="${pageContext.request.contextPath}/order/settings"><i class="fas fa-cog"></i>订单管理设置</a></li>
      </ul>
    </li>
  </ul>
  <div class="user-section">
    <c:choose>
      <c:when test="${empty sessionScope.user}">
        <div class="login-button">
          <i class="fas fa-sign-in-alt"></i>
          <a href="${pageContext.request.contextPath}/user/toLogin">登录</a>
        </div>
      </c:when>
      <c:otherwise>
        <div class="user-info">
          <div class="user-avatar">
            <img src="${pageContext.request.contextPath}/static/images/avatar.png" alt="用户头像">
          </div>
          <div class="user-details">
            <span class="user-name">${sessionScope.user.username}</span>
            <a href="${pageContext.request.contextPath}/user/logout" class="logout-link">
              <i class="fas fa-sign-out-alt"></i> 退出
            </a>
          </div>
        </div>
      </c:otherwise>
    </c:choose>
  </div>
</nav>

<script>
  // 添加下拉菜单的交互效果
  document.addEventListener('DOMContentLoaded', function() {
    const dropdowns = document.querySelectorAll('.dropdown');
    dropdowns.forEach(function(dropdown) {
      dropdown.addEventListener('click', function() {
        this.classList.toggle('active');
      });
    });
  });
</script> 